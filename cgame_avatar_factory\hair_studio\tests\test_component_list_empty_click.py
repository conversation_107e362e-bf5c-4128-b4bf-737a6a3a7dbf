"""Test Component List Empty Area Click Functionality.

This module tests the new functionality for clearing selection when clicking
on empty areas of the component list.
"""

# Import standard library
import logging
import sys
import unittest
from unittest.mock import Mock, patch

# Add project root to path
import os
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", ".."))
sys.path.insert(0, project_root)

# Import third-party modules
from qtpy import QtCore, QtWidgets, QtGui
from qtpy.QtCore import Qt

# Import local modules
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList


class TestComponentListEmptyClick(unittest.TestCase):
    """Test component list empty area click functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create QApplication if it doesn't exist
        self.app = QtWidgets.QApplication.instance()
        if self.app is None:
            self.app = QtWidgets.QApplication([])

        # Create component list widget with required hair_type parameter
        self.component_list = ComponentList(hair_type="card")
        
        # Create test component data
        self.test_components = [
            {
                "id": "test_component_1",
                "name": "Test Hair 1",
                "asset_type": "hair_card",
                "is_viewed": True,
                "node_names": {"hair_asset": "test_hair_1", "ref_head": "test_head_1"}
            },
            {
                "id": "test_component_2", 
                "name": "Test Hair 2",
                "asset_type": "hair_card",
                "is_viewed": True,
                "node_names": {"hair_asset": "test_hair_2", "ref_head": "test_head_2"}
            }
        ]
        
        # Set up components in the list
        self.component_list.update_components(self.test_components)

    def tearDown(self):
        """Clean up after tests."""
        if hasattr(self, 'component_list'):
            self.component_list.deleteLater()

    def test_clear_selection_method(self):
        """Test the _clear_selection method directly."""
        # Mock the component_selected signal
        self.component_list.component_selected = Mock()
        
        # First select a component
        self.component_list._select_component_in_ui("test_component_1")
        
        # Verify component is selected
        selection_model = self.component_list.component_list.selectionModel()
        self.assertTrue(selection_model.hasSelection())
        
        # Clear selection
        self.component_list._clear_selection()
        
        # Verify selection is cleared
        self.assertFalse(selection_model.hasSelection())
        
        # Verify signal was emitted with None
        self.component_list.component_selected.emit.assert_called_with(None)

    def test_empty_area_click_detection(self):
        """Test detection of clicks on empty areas."""
        # Create a mock mouse event at a position with no item
        mock_event = Mock()
        mock_event.type.return_value = QtCore.QEvent.MouseButtonPress
        mock_event.button.return_value = Qt.LeftButton
        mock_event.pos.return_value = QtCore.QPoint(10, 1000)  # Position beyond items
        
        # Mock indexAt to return invalid index (empty area)
        with patch.object(self.component_list.component_list, 'indexAt') as mock_index_at:
            mock_index_at.return_value = QtCore.QModelIndex()  # Invalid index
            
            # Mock _clear_selection to verify it's called
            with patch.object(self.component_list, '_clear_selection') as mock_clear:
                # Process the event through eventFilter
                result = self.component_list.eventFilter(self.component_list.component_list, mock_event)
                
                # Verify the event was handled and clear_selection was called
                self.assertTrue(result)
                mock_clear.assert_called_once()

    def test_item_click_not_affected(self):
        """Test that clicking on items still works normally."""
        # Create a real QMouseEvent instead of Mock
        mouse_event = QtGui.QMouseEvent(
            QtCore.QEvent.MouseButtonPress,
            QtCore.QPoint(10, 10),
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )

        # Mock indexAt to return valid index (item area)
        valid_index = self.component_list.component_model.index(0, 0)
        with patch.object(self.component_list.component_list, 'indexAt') as mock_index_at:
            mock_index_at.return_value = valid_index

            # Mock _clear_selection to verify it's NOT called
            with patch.object(self.component_list, '_clear_selection') as mock_clear:
                # Process the event through eventFilter
                result = self.component_list.eventFilter(self.component_list.component_list, mouse_event)

                # Verify the event was NOT handled by our empty area logic
                self.assertFalse(result)
                mock_clear.assert_not_called()

    def test_right_click_not_affected(self):
        """Test that right-clicks are not affected by empty area logic."""
        # Create a real QMouseEvent for right-click
        mouse_event = QtGui.QMouseEvent(
            QtCore.QEvent.MouseButtonPress,
            QtCore.QPoint(10, 1000),
            Qt.RightButton,
            Qt.RightButton,
            Qt.NoModifier
        )

        # Mock indexAt to return invalid index
        with patch.object(self.component_list.component_list, 'indexAt') as mock_index_at:
            mock_index_at.return_value = QtCore.QModelIndex()

            # Mock _clear_selection to verify it's NOT called
            with patch.object(self.component_list, '_clear_selection') as mock_clear:
                # Process the event through eventFilter
                result = self.component_list.eventFilter(self.component_list.component_list, mouse_event)

                # Verify right-click doesn't trigger clear selection
                self.assertFalse(result)
                mock_clear.assert_not_called()

    def test_keyboard_events_still_work(self):
        """Test that keyboard events are still handled correctly."""
        # Create a mock keyboard event
        mock_event = Mock()
        mock_event.type.return_value = QtCore.QEvent.KeyPress
        mock_event.key.return_value = Qt.Key_Delete
        mock_event.modifiers.return_value = Qt.NoModifier
        
        # Mock the delete method
        with patch.object(self.component_list, '_delete_selected_component') as mock_delete:
            # Process the event through eventFilter
            result = self.component_list.eventFilter(self.component_list.component_list, mock_event)
            
            # Verify keyboard event was handled
            self.assertTrue(result)
            mock_delete.assert_called_once()


if __name__ == '__main__':
    # Set up logging for tests
    logging.basicConfig(level=logging.DEBUG)
    unittest.main()
