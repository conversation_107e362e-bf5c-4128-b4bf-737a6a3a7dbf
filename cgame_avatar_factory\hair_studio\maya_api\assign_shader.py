# Import built-in modules
import logging
import os

# Import local modules
import cgame_avatar_factory.hair_studio.constants as const

from .utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)


def create_dx11shader_with_tex_params(fx_path, shader_name="myDX11Shader", texture_path=None, texture_path_dict=None, param_dict=None):
    """
    Assign a dx11Shader to the specified mesh and set shader parameters.

    Args:
        fx_path (str): Path to the .fx file for the dx11Shader.
        shader_name (str, optional): Name of the dx11Shader node. Defaults to "myDX11Shader".
        param_dict (dict, optional): Dictionary of shader parameter names to values. Supported value types:
            - str (image file path): Automatically connects a file node as texture.
            - float or int: Sets the attribute directly.
            - tuple or list of length 3: Sets as a color attribute.
            - enum (int or str): Sets enum attribute by index or name.

    Returns:
        All created maya nodes.

    Notes:
        - If the dx11Shader plugin is not loaded, attempts to load it.
        - Warns if mesh or shader attributes do not exist.
        - Logs warnings for missing files or invalid parameter assignments.
    """
    logger = logging.getLogger(__name__)
     # Handle case where texture_path is None (no texture directory found)
    if texture_path and os.path.exists(texture_path):
        texture_path_dict = find_texture_path(texture_path, texture_path_dict)
    else:
        # If no texture path provided, initialize with None values
        logger.warning(f"No valid texture path provided for {texture_path}, using default parameters only")
        texture_path_dict = {key: None for key in texture_path_dict}

    param_dict = param_dict if param_dict else {}

    param_dict.update(texture_path_dict)

    logger.debug("final_param_dict: {}".format(param_dict))
    # Ensure dx11Shader plugin is loaded
    if not cmds.pluginInfo("dx11Shader", query=True, loaded=True):
        try:
            cmds.loadPlugin("dx11Shader")
        except:
            cmds.warning("can not load dx11Shader plugin")
            return

    # create or get dx11Shader node
    if cmds.objExists(shader_name):
        shader = shader_name
    else:
        shader = cmds.shadingNode("dx11Shader", asShader=True, name=shader_name)

    # set .fx path
    if cmds.attributeQuery("shader", node=shader, exists=True):
        cmds.setAttr(shader + ".shader", fx_path, type="string")
    else:
        cmds.warning("dx11Shader node has no 'shader' attribute, may not load dx11Shader plugin.")
        return

    # process parameters
    restore_all_nodes = connect_params_to_shader(shader, param_dict)

    return restore_all_nodes, shader


def connect_texture_out_to_shader(file_node, out_attrs, attr_full):
    """Connect texture output to shader attribute.
    Args:
        file_node (str): Name of the file node.
        out_attrs (list): List of output attributes to try.
        attr_full (str): Full name of the shader attribute to connect to.
    """

    for out_attr in out_attrs:
        try:
            cmds.connectAttr(file_node + "." + out_attr, attr_full, force=True)
            break
        except:
            cmds.warning(
                "can not connect texture {0} to attribute: {1}, will try next attribute...".format(out_attr, attr_full),
            )


def connect_params_to_shader(shader_name, param_dict=None):
    """
    Connect parameters to shader.
    Args:
        shader_name (str): Name of the shader node.
        param_dict (dict, optional): Dictionary of shader parameter names to values. Supported value types:
            - str (image file path): Automatically connects a file node as texture.
            - float or int: Sets the attribute directly.
            - tuple or list of length 3: Sets as a color attribute.
            - enum (int or str): Sets enum attribute by index or name.

    Returns:
        list: List of all created file nodes.
    """

    if not param_dict:
        return []

    restore_file_nodes = []

    for attr, value in param_dict.items():
        if not cmds.attributeQuery(attr, node=shader_name, exists=True):
            cmds.warning("dx11Shader node has no attribute: %s" % attr)
            continue

        attr_full = "{0}.{1}".format(shader_name, attr)

        # 1. texture (string and image file)
        if isinstance(value, str) and value.lower().endswith(
            (".png", ".jpg", ".tga", ".tif", ".exr", ".bmp", ".dds"),
        ):
            # NOTE: check file path exists
            if not os.path.exists(value):
                cmds.warning("texture file not exists: %s" % value)
                continue
            # find existing file node
            connections = cmds.listConnections(attr_full, type="file")
            if connections:
                file_node = connections[0]
                cmds.setAttr(file_node + ".fileTextureName", value, type="string")
            else:
                file_node = cmds.shadingNode("file", asTexture=True, isColorManaged=True)
                restore_file_nodes.append(file_node)
                cmds.setAttr(file_node + ".fileTextureName", value, type="string")
                # connect to shader
                if "transparency" in attr:
                    out_attrs = ["outTransparency", "outAlpha"]
                else:
                    out_attrs = ["outColor"]
                connect_texture_out_to_shader(file_node, out_attrs, attr_full)

            continue

        attr_type = cmds.getAttr(attr_full, type=True)
        # 2. color/float3[]/double3/float3[]/color
        if isinstance(value, (tuple, list)) and len(value) == 3 and attr_type in ["float3", "double3", "color"]:
            cmds.setAttr(attr_full, value[0], value[1], value[2], type=attr_type)
            continue

        # 3. float/int
        if isinstance(value, (float, int)) and attr_type in ["float", "double", "long", "short", "byte"]:
            cmds.setAttr(attr_full, value)
            continue

        # 4. enum
        if attr_type == "enum":
            # value can be int or str (enum name)
            if isinstance(value, int):
                cmds.setAttr(attr_full, value)
            elif isinstance(value, str):
                # get all enum names
                enum_names = cmds.attributeQuery(attr, node=shader_name, listEnum=True)[0].split(":")
                if value in enum_names:
                    idx = enum_names.index(value)
                    cmds.setAttr(attr_full, idx)
                else:
                    cmds.warning("enum value %s not in attribute %s options: %s" % (value, attr, enum_names))
            continue

        # 5. other types
        try:
            cmds.setAttr(attr_full, value)
        except Exception as e:
            cmds.warning("can not set attribute: %s, error: %s" % (attr_full, e))

    return restore_file_nodes


def create_blinn_material_with_texture(shader_name, texture_path, texture_dict):
    """
    Create a blinn material and set texture parameters.
    """
    param_dict = find_texture_path(texture_path, texture_dict)
    # create blinn material
    blinn_material = cmds.shadingNode("blinn", asShader=True, name=shader_name)

    # process parameters
    restore_all_nodes = connect_params_to_shader(blinn_material, param_dict)

    return restore_all_nodes, blinn_material


def find_texture_path(root_path, name_dict):
    """
    Recursively search for files under root_path whose filename (without extension) ends with any key in name_dict.
    Return a dict: {key: file_path or None, ...}
    """
    logger = logging.getLogger(__name__)
    result = {key: None for key in name_dict}

    # Handle None or invalid root_path
    if not root_path or not isinstance(root_path, str):
        logger.warning(f"Invalid root_path provided: {root_path}")
        return result

    if not os.path.isdir(root_path):
        logger.warning(f"Root path does not exist or is not a directory: {root_path}")
        return result
    found_keys = set()
    for dirpath, _, filenames in os.walk(root_path):
        for filename in filenames:
            name_no_ext, _ = os.path.splitext(filename)
            for key, value in name_dict.items():
                if value in found_keys:
                    continue
                if name_no_ext.endswith(value):
                    file_path = os.path.join(dirpath, filename)
                    result[key] = file_path
                    found_keys.add(key)
                    logger.debug(f"Found file (no ext match) ending with '{key}': {file_path}")
            if len(found_keys) == len(name_dict):
                break
        if len(found_keys) == len(name_dict):
            break
    return result



def assign_material_to_mesh(mesh_name, texture_path, shader_name=None, asset_type="hair", shader_engine=None):
    """Assign material to mesh.

    Args:
        mesh_name (str): mesh to assign material
        texture_path (str): all textures in same directory
        shader_name (str, optional): shader name. Defaults to None.
        shader_type (str, optional): shader type. Defaults to "dx11Shader".

    Returns:
        tuple: (shader_name, restore_all_nodes)
    """
    logger = logging.getLogger(__name__)

    shader_type = const.SUB_ASSET_SHADER_MAP.get(asset_type, "dx11Shader")

    if not shader_name:
        shader_name = "{}_shader".format(mesh_name)

    if shader_type == "blinn":
        blinn_dict = {
            "color": "_diffuse",
            "transparency": "_opacity",
            "specularColor": "_metallic",
        }
        restore_all_nodes, shader_node = create_blinn_material_with_texture(shader_name, texture_path, blinn_dict)
    elif shader_type is dict:
        replace_shading_node(mesh_name, shader_type, texture_path, shader_name)
    else: 
        # NOTE: find texture path
        texture_path_dict = {
            "HairAlphaTexture": "_opacity",
            "HairRootTexture": "_HairRootMap",
            "HairIDTexture": "_HairIDMap",
            "HairAOTexture": "_ao",
        }
        param_dict = {
            "UseHairAlphaTexture": True,
            "RootColor": (0.21, 0.14, 0.09),
            "TipColor": (0.275, 0.186, 0.127),
            "Brightness": 1.0,
            "Scatter": 0.65,
            "shadowMultiplier": 0.8,
            "Roughness": 0.6,
        }
        dx11_shader_path = os.path.join(
            const.DEFAULT_SHADER_DIR,
            const.HAIR_SHADER_NAME,
        )
        restore_all_nodes, shader_node = create_dx11shader_with_tex_params(
            fx_path=dx11_shader_path,
            shader_name=shader_name,
            texture_path=texture_path,
            texture_path_dict=texture_path_dict,
            param_dict=param_dict,
        )

    # check mesh exists
    if not cmds.objExists(mesh_name):
        cmds.warning("mesh not exists: %s" % mesh_name)
        return None, []

    # create Shading Group and connect
    sg_name = shader_engine if shader_engine else shader_node + "SG"
    if not cmds.objExists(sg_name):
        sg_name = cmds.sets(shader_node, renderable=True, noSurfaceShader=True, empty=True, name=sg_name)
        logger.debug("create shader engine for sg_name: {}".format(sg_name))

    cmds.connectAttr(shader_node + ".outColor", sg_name + ".surfaceShader", force=True)
    # assign material to mesh
    cmds.sets(mesh_name, edit=True, forceElement=sg_name)

    return shader_name, restore_all_nodes


def get_all_shadings(obj):
    sg_mat_list = []
    import pymelc.core as pm
    sg_list = pm.mel.getConnectedShaders(obj,0)
    sg_all = cmds.sets(obj, q=True, renderable=True)
    print(sg_all)
    for sg in sg_list:
        # mats = (pm.PyNode(sg).listConnections(type="shadingDependNode"))
        surface_mat = cmds.listConnections(sg + '.surfaceShader')
        print(surface_mat)
        
        print(sg)
        # print(mats)

        connected_faces = pm.sets(sg, query=True)
        print(connected_faces)
        sg_mat_list.append((sg, surface_mat, connected_faces))

    return sg_mat_list

def replace_shading_node(obj_name, shader_type, texture_path, shader_name=None):

    all_shading = get_all_shadings(obj_name)    
    # find base mat
    base_shading = None
    default_shading = shader_type.get("default", "dx11Shader")
    for sg, mat, _ in all_shading:
        if "_Base" in mat.name():
            # give base shade attribute
            blinn_dict = {
                "color": "_diffuse",
                "transparency": "_opacity",
                "specularColor": "_metallic",
            }
            restore_all_nodes, shader_node = create_blinn_material_with_texture(shader_name, texture_path, blinn_dict)
        else:
            # other shade, replace
            # NOTE: find texture path
            texture_path_dict = {
                "HairAlphaTexture": "_opacity",
                "HairRootTexture": "_HairRootMap",
                "HairIDTexture": "_HairIDMap",
                "HairAOTexture": "_ao",
            }
            param_dict = {
                "UseHairAlphaTexture": True,
                "RootColor": (0.21, 0.14, 0.09),
                "TipColor": (0.275, 0.186, 0.127),
                "Brightness": 1.0,
                "Scatter": 0.65,
                "shadowMultiplier": 0.8,
                "Roughness": 0.6,
            }
            dx11_shader_path = os.path.join(
                const.DEFAULT_SHADER_DIR,
                const.HAIR_SHADER_NAME,
            )
            restore_all_nodes, shader_node = create_dx11shader_with_tex_params(
                fx_path=dx11_shader_path,
                shader_name=shader_name,
                texture_path=texture_path,
                texture_path_dict=texture_path_dict,
                param_dict=param_dict,
            )
        cmds.connectAttr(shader_node + ".outColor", sg + ".surfaceShader", force=True)
        
    