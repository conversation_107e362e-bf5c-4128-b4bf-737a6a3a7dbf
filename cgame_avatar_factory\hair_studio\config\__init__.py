"""Hair Studio Configuration Package.

This package contains configuration modules for the Hair Studio tool.

使用场景说明:
1. register_shader_config: 运行时动态注册新shader配置
   - 插件系统: 第三方插件注册自定义shader
   - 项目定制: 特定项目需要的特殊shader
   - 用户配置: 用户自定义的shader参数组合

2. create_config_with_overrides: 基于现有配置创建变体
   - 材质变体: 同一基础shader的不同参数组合
   - 质量等级: 高中低质量的shader变体
   - 平台适配: 不同平台的shader参数调整

3. get_preset_config: 使用预定义的shader变体
   - 快速应用: 使用预设的常用shader组合
   - 标准化: 确保项目中使用统一的shader变体
"""

from .shader_configs import (
    get_shader_config,
    register_shader_config,
    create_config_with_overrides,
    get_preset_config,
    shader_registry,
    ASSET_SHADER_MAPPING,
    PARAMETER_OVERRIDES,
)

__all__ = [
    "get_shader_config",
    "register_shader_config",
    "create_config_with_overrides",
    "get_preset_config",
    "shader_registry",
    "ASSET_SHADER_MAPPING",
    "PARAMETER_OVERRIDES",
]
