"""Shader Configuration Module.

This module provides centralized shader configuration management for the Hair Studio.
It uses configurations defined in constants.py and provides a unified interface.

配置系统结构说明:
1. constants.py 中定义所有基础配置常量
2. 本模块提供配置管理和扩展功能
3. assign_shader.py 通过本模块访问配置
"""

import os
from typing import Dict, Any

# Import constants for shader configurations
import cgame_avatar_factory.hair_studio.constants as const


class ShaderConfigRegistry:
    """Shader配置注册器，提供配置验证和扩展支持."""

    def __init__(self):
        self._configs = {}
        self._load_default_configs()

    def _load_default_configs(self):
        """从constants.py加载默认shader配置."""
        # 使用constants.py中的SHADER_CONFIGS
        self._configs.update(const.SHADER_CONFIGS)
    
    def register_config(self, key: str, config: Dict[str, Any]):
        """Register a new shader configuration.
        
        Args:
            key: Configuration key
            config: Shader configuration dictionary
        """
        self._validate_config(config)
        self._configs[key] = config
    
    def get_config(self, key: str) -> Dict[str, Any]:
        """Get shader configuration by key.
        
        Args:
            key: Configuration key
            
        Returns:
            Shader configuration dictionary
        """
        if key not in self._configs:
            raise KeyError(f"Unknown shader config: {key}")
        
        config = self._configs[key].copy()
        
        # Build full fx path if needed
        if config.get("fx_path") and not os.path.isabs(config["fx_path"]):
            config["fx_path"] = os.path.join(const.DEFAULT_SHADER_DIR, config["fx_path"])
            
        return config
    
    def list_configs(self) -> list:
        """List all available configuration keys."""
        return list(self._configs.keys())
    
    def _validate_config(self, config: Dict[str, Any]):
        """Validate shader configuration structure."""
        required_keys = ["shader_type", "texture_map", "params"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required key in shader config: {key}")


# ============================================================================
# 配置常量引用 - 直接使用constants.py中的定义，避免重复
# ============================================================================

# 从constants.py导入配置映射
ASSET_SHADER_MAPPING = const.SUB_ASSET_SHADER_MAP

# 参数覆盖配置示例 - 用于创建shader变体
PARAMETER_OVERRIDES = {
    "eyebrow_fine": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.8,
            "Scatter": 0.3,
        }
    },
    "beard_coarse": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.9,
            "RootColor": (0.15, 0.10, 0.05),
            "TipColor": (0.20, 0.15, 0.10),
        }
    },
}

# Global registry instance - will be initialized after DEFAULT_SHADER_CONFIGS is defined
shader_registry = None


def _get_shader_registry():
    """Get or create the global shader registry."""
    global shader_registry
    if shader_registry is None:
        shader_registry = ShaderConfigRegistry()
    return shader_registry


def get_shader_config(key: str) -> Dict[str, Any]:
    """Get shader configuration by key.

    Args:
        key: Configuration key

    Returns:
        Shader configuration dictionary
    """
    return _get_shader_registry().get_config(key)


def register_shader_config(key: str, config: Dict[str, Any]):
    """Register a new shader configuration.

    Args:
        key: Configuration key
        config: Shader configuration dictionary
    """
    _get_shader_registry().register_config(key, config)


def create_config_with_overrides(base_key: str, overrides: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new configuration by applying overrides to a base configuration.
    
    Args:
        base_key: Base configuration key
        overrides: Parameter overrides to apply
        
    Returns:
        New configuration with overrides applied
    """
    base_config = get_shader_config(base_key)
    new_config = base_config.copy()
    
    # Apply parameter overrides
    if "params" in overrides:
        new_config["params"] = base_config["params"].copy()
        new_config["params"].update(overrides["params"])
    
    # Apply other overrides
    for key, value in overrides.items():
        if key != "params":
            new_config[key] = value
    
    return new_config
