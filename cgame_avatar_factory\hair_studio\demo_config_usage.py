#!/usr/bin/env python
"""
🎯 Shader配置系统使用演示

这个脚本演示了配置系统的实际工作流程，展示constants.py中的SHADER_CONFIGS如何被使用。
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def demo_config_structure():
    """演示配置结构和关系"""
    print("=" * 60)
    print("🏗️  配置结构演示")
    print("=" * 60)
    
    # 导入配置
    import cgame_avatar_factory.hair_studio.constants as const
    
    print("📄 constants.py 中的核心配置：")
    print(f"   SHADER_CONFIGS 包含 {len(const.SHADER_CONFIGS)} 个shader配置:")
    for key in const.SHADER_CONFIGS.keys():
        print(f"   - {key}")
    
    print(f"\n   SUB_ASSET_SHADER_MAP 包含 {len(const.SUB_ASSET_SHADER_MAP)} 个资产映射:")
    for asset_type, shader_config in const.SUB_ASSET_SHADER_MAP.items():
        if isinstance(shader_config, dict):
            print(f"   - {asset_type}: 复杂映射 {shader_config}")
        else:
            print(f"   - {asset_type}: {shader_config}")

def demo_config_access():
    """演示配置访问流程"""
    print("\n" + "=" * 60)
    print("🔗 配置访问流程演示")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.config import get_shader_config
        import cgame_avatar_factory.hair_studio.constants as const
        
        # 演示配置访问链路
        print("🎯 演示：获取头发shader配置")
        print("   1. 用户指定 asset_type='hair'")
        
        # 步骤1：资产类型映射
        asset_type = "hair"
        config_key = const.SUB_ASSET_SHADER_MAP.get(asset_type)
        print(f"   2. SUB_ASSET_SHADER_MAP['{asset_type}'] → '{config_key}'")
        
        # 步骤2：获取完整配置
        config = get_shader_config(config_key)
        print(f"   3. SHADER_CONFIGS['{config_key}'] → 完整配置")
        print(f"      - shader_type: {config['shader_type']}")
        print(f"      - texture_map: {len(config['texture_map'])} 个纹理映射")
        print(f"      - params: {len(config['params'])} 个参数")
        print(f"      - fx_path: {config['fx_path']}")
        
        # 演示纹理映射
        print(f"\n   📋 纹理映射详情:")
        for attr, suffix in config['texture_map'].items():
            print(f"      {attr} → 查找文件后缀 '{suffix}'")
        
        # 演示参数配置
        print(f"\n   ⚙️  参数配置详情:")
        for param, value in config['params'].items():
            print(f"      {param}: {value}")
            
    except Exception as e:
        print(f"❌ 配置访问演示失败: {e}")

def demo_complex_mapping():
    """演示复杂映射（如eyebrow）"""
    print("\n" + "=" * 60)
    print("🎭 复杂映射演示 (eyebrow)")
    print("=" * 60)
    
    try:
        import cgame_avatar_factory.hair_studio.constants as const
        from cgame_avatar_factory.hair_studio.config import get_shader_config
        
        asset_type = "eyebrow"
        mapping = const.SUB_ASSET_SHADER_MAP[asset_type]
        
        print(f"🎯 eyebrow 资产的复杂映射: {mapping}")
        
        # 模拟不同材质名称的处理
        test_materials = ["eyebrow_Base_mat", "eyebrow_hair_mat", "eyebrow_other_mat"]
        
        for mat_name in test_materials:
            print(f"\n   📝 材质: {mat_name}")
            
            # 确定使用哪个配置
            config_key = mapping.get("default", "dx11_hair")  # 默认配置
            for pattern, pattern_config in mapping.items():
                if pattern != "default" and pattern in mat_name:
                    config_key = pattern_config
                    break
            
            print(f"      → 使用配置: {config_key}")
            
            # 获取配置详情
            config = get_shader_config(config_key)
            print(f"      → shader类型: {config['shader_type']}")
            
    except Exception as e:
        print(f"❌ 复杂映射演示失败: {e}")

def demo_usage_examples():
    """演示实际使用示例"""
    print("\n" + "=" * 60)
    print("🚀 实际使用示例")
    print("=" * 60)
    
    print("💡 在实际代码中的使用方式：")
    print("""
# 🎯 简单使用 - 头发材质
from cgame_avatar_factory.hair_studio.maya_api.assign_shader import assign_material_to_mesh

shader_name, nodes = assign_material_to_mesh(
    mesh_name="hair_mesh_001",
    texture_path="/path/to/textures", 
    asset_type="hair"  # 自动使用constants.SHADER_CONFIGS["dx11_hair"]
)

# 🎯 复杂映射 - 眉毛材质  
shader_name, nodes = assign_material_to_mesh(
    mesh_name="eyebrow_mesh_001",
    texture_path="/path/to/textures",
    asset_type="eyebrow"  # 根据材质名称自动选择blinn或dx11_hair
)

# 🎯 扩展配置 - 添加新shader类型
import cgame_avatar_factory.hair_studio.constants as const

# 方法1：直接修改constants.py
const.SHADER_CONFIGS["custom_shader"] = {
    "shader_type": "lambert",
    "texture_map": {"color": "_diffuse"},
    "params": {"transparency": 0.5},
    "fx_path": None,
}

# 方法2：使用配置管理器
from cgame_avatar_factory.hair_studio.config import register_shader_config

register_shader_config("runtime_shader", {
    "shader_type": "phong", 
    "texture_map": {"color": "_albedo"},
    "params": {"shininess": 0.8},
    "fx_path": None,
})
""")

def main():
    """主函数"""
    print("🎯 Shader配置系统演示")
    print("展示constants.py中的SHADER_CONFIGS如何被使用\n")
    
    try:
        demo_config_structure()
        demo_config_access()
        demo_complex_mapping()
        demo_usage_examples()
        
        print("\n" + "=" * 60)
        print("✅ 演示完成！")
        print("=" * 60)
        print("📋 总结：")
        print("   1. constants.py 中的 SHADER_CONFIGS 是核心数据源")
        print("   2. config/shader_configs.py 提供管理和访问接口")
        print("   3. assign_shader.py 使用配置创建和分配shader")
        print("   4. 整个系统通过配置驱动，易于扩展和维护")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
