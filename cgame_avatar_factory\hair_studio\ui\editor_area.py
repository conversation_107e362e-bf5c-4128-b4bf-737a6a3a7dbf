"""Editor Area Module.

This module provides the editor area widget for the Hair Studio tool.
It allows users to view and edit the properties of the selected hair component.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets import MCheckBox
from dayu_widgets import MDoubleSpinBox
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>
from dayu_widgets import MLineEdit
from dayu_widgets import MSpinBox
# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_COMPONENT_WIDTH
from cgame_avatar_factory.hair_studio.constants import DEFAULT_CURVE_THICKNESS
from cgame_avatar_factory.hair_studio.constants import DEFAULT_XGEN_DENSITY
from cgame_avatar_factory.hair_studio.constants import DEFAULT_XGEN_LENGTH
from cgame_avatar_factory.hair_studio.constants import FOCUS_POLICY_NO_FOCUS
from cgame_avatar_factory.hair_studio.constants import FORM_INPUT_HEIGHT
from cgame_avatar_factory.hair_studio.constants import FORM_INPUT_MIN_WIDTH
from cgame_avatar_factory.hair_studio.constants import FORM_INPUT_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_DENSITY
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_HEIGHT
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_LENGTH
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_NAME
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_SUBDIVISIONS
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_THICKNESS
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_VISIBLE
from cgame_avatar_factory.hair_studio.constants import FORM_LABEL_WIDTH
from cgame_avatar_factory.hair_studio.constants import FORM_MARGIN_DEFAULT
from cgame_avatar_factory.hair_studio.constants import FORM_SEPARATOR_STYLE
from cgame_avatar_factory.hair_studio.constants import FORM_SPACING_DEFAULT
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_MEDIUM
from cgame_avatar_factory.hair_studio.constants import MAX_CLEAR_ITERATIONS
from cgame_avatar_factory.hair_studio.constants import MAX_COMPONENT_SIZE
from cgame_avatar_factory.hair_studio.constants import MAX_CURVE_SUBDIVISIONS
from cgame_avatar_factory.hair_studio.constants import MAX_CURVE_THICKNESS
from cgame_avatar_factory.hair_studio.constants import MAX_RECURSION_DEPTH
from cgame_avatar_factory.hair_studio.constants import MAX_XGEN_DENSITY
from cgame_avatar_factory.hair_studio.constants import MIN_COMPONENT_SIZE
from cgame_avatar_factory.hair_studio.constants import MIN_CURVE_SUBDIVISIONS
from cgame_avatar_factory.hair_studio.constants import MIN_CURVE_THICKNESS
from cgame_avatar_factory.hair_studio.constants import MIN_XGEN_DENSITY
from cgame_avatar_factory.hair_studio.constants import NO_COMPONENT_STYLE
from cgame_avatar_factory.hair_studio.constants import PROPERTY_DENSITY
from cgame_avatar_factory.hair_studio.constants import PROPERTY_HEIGHT
from cgame_avatar_factory.hair_studio.constants import PROPERTY_LENGTH
from cgame_avatar_factory.hair_studio.constants import PROPERTY_NAME
from cgame_avatar_factory.hair_studio.constants import PROPERTY_SUBDIVISIONS
from cgame_avatar_factory.hair_studio.constants import PROPERTY_THICKNESS
from cgame_avatar_factory.hair_studio.constants import PROPERTY_VISIBLE
from cgame_avatar_factory.hair_studio.constants import PROPERTY_WIDTH
from cgame_avatar_factory.hair_studio.constants import SUBTITLE_STYLE
from cgame_avatar_factory.hair_studio.constants import \
    DEFAULT_COMPONENT_HEIGHT  # Focus control constants
from cgame_avatar_factory.hair_studio.constants import \
    DEFAULT_CURVE_SUBDIVISIONS
from cgame_avatar_factory.hair_studio.constants import \
    FOCUS_OUTLINE_DISABLED_STYLE
from cgame_avatar_factory.hair_studio.constants import \
    UI_TEXT_NO_COMPONENT_SELECTED
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class EditorArea(QtWidgets.QScrollArea):
    """Editor Area Widget.

    This widget displays and allows editing of the properties of the selected hair component.
    It shows different properties based on the type of hair component selected.
    """

    def __init__(self, hair_type, hair_manager=None, parent=None):
        """Initialize the EditorArea.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(EditorArea, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}EditorArea".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize manager
        self.manager = hair_manager if hair_manager is not None else HairManager(parent)

        # Current component data
        self.current_component = None

        # Initialize UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface components."""
        # Create container widget
        container = QtWidgets.QWidget()
        self.setWidget(container)
        self.setWidgetResizable(True)

        # Remove focus outline from scroll area to prevent blue border
        self.setFocusPolicy(QtCore.Qt.FocusPolicy(FOCUS_POLICY_NO_FOCUS))
        container.setFocusPolicy(QtCore.Qt.FocusPolicy(FOCUS_POLICY_NO_FOCUS))

        # Apply form input styles and focus outline removal to the container
        container.setStyleSheet(
            FORM_INPUT_STYLE + FORM_LABEL_STYLE + FORM_SEPARATOR_STYLE + FOCUS_OUTLINE_DISABLED_STYLE,
        )

        # Main layout with consistent spacing
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
            LAYOUT_SPACING_MEDIUM,
        )
        main_layout.setSpacing(LAYOUT_SPACING_MEDIUM)

        # Title with consistent styling
        self.title_label = MLabel("毛发编辑区")
        self.title_label.setProperty("h2", True)
        self.title_label.setStyleSheet(SUBTITLE_STYLE)
        self.title_label.setAlignment(QtCore.Qt.AlignCenter)
        main_layout.addWidget(self.title_label)

        # Add separator with consistent styling
        separator = self._create_separator()
        main_layout.addWidget(separator)

        # Form layout for properties with improved alignment
        self.form_layout = QtWidgets.QFormLayout()
        self.form_layout.setLabelAlignment(
            QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter,
        )
        self.form_layout.setFormAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignTop)
        self.form_layout.setContentsMargins(
            FORM_MARGIN_DEFAULT,
            FORM_MARGIN_DEFAULT,
            FORM_MARGIN_DEFAULT,
            FORM_MARGIN_DEFAULT,
        )
        self.form_layout.setSpacing(FORM_SPACING_DEFAULT)

        # Set consistent field growth policy
        self.form_layout.setFieldGrowthPolicy(QtWidgets.QFormLayout.ExpandingFieldsGrow)

        # Add form layout to scroll area
        main_layout.addLayout(self.form_layout)

        # Add stretch to push content to the top
        main_layout.addStretch()

        # Set initial state
        self.set_component(None)

    def _create_separator(self):
        """Create a styled separator widget."""
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        return separator

    def _create_form_input(self, widget_type, **kwargs):
        """Create a styled form input widget.

        Args:
            widget_type: The widget class to create (MLineEdit, MSpinBox, etc.)
            **kwargs: Additional arguments to pass to the widget constructor

        Returns:
            The created and styled widget
        """
        widget = widget_type(**kwargs)

        # Set minimum size for consistency
        widget.setMinimumWidth(FORM_INPUT_MIN_WIDTH)
        widget.setMinimumHeight(FORM_INPUT_HEIGHT)

        # Set size policy for responsive behavior
        widget.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Fixed,
        )

        return widget

    def set_component(self, component_data):
        """Set the current component to edit.

        Args:
            component_data (dict): Dictionary containing component data
        """
        self.current_component = component_data

        # Clear existing widgets
        self._clear_form()

        if not component_data:
            self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)
            # Apply no component selected style for consistent height
            self.title_label.setStyleSheet(NO_COMPONENT_STYLE)
            return

        # Update title
        self.title_label.setText(component_data.get("name", "Unnamed Component"))
        # Apply subtitle style for component name
        self.title_label.setStyleSheet(SUBTITLE_STYLE)

        # Add common properties
        self._add_common_properties(component_data)

        # Add type-specific properties
        if self.hair_type == HAIR_TYPE_CARD:
            self._add_card_properties(component_data)
        elif self.hair_type == HAIR_TYPE_XGEN:
            self._add_xgen_properties(component_data)
        elif self.hair_type == HAIR_TYPE_CURVE:
            self._add_curve_properties(component_data)

    def clear_component(self):
        """Clear the current component and reset the editor area."""
        self.current_component = None

        # Clear existing widgets
        self._clear_form()

        # Update title
        self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)
        # Apply no component selected style for consistent height
        self.title_label.setStyleSheet(NO_COMPONENT_STYLE)

    def _clear_form(self):
        """Clear all widgets from the form layout."""
        try:
            # Add safety counter to prevent infinite loops
            max_iterations = MAX_CLEAR_ITERATIONS
            iteration = 0

            while self.form_layout.count() > 0 and iteration < max_iterations:
                item = self.form_layout.takeAt(0)
                if item is None:
                    break

                if item.widget():
                    item.widget().deleteLater()
                elif item.layout():
                    self._clear_layout(item.layout())

                iteration += 1

            if iteration >= max_iterations:
                self._logger.warning(
                    f"Warning: _clear_form reached maximum iterations ({max_iterations})",
                )

        except Exception as e:
            self._logger.error(f"Error in _clear_form: {e}")

    def _clear_layout(self, layout):
        """Recursively clear a layout and its widgets."""
        try:
            if layout is None:
                return

            # Add safety counter to prevent infinite loops
            max_iterations = MAX_CLEAR_ITERATIONS
            iteration = 0

            while layout.count() > 0 and iteration < max_iterations:
                item = layout.takeAt(0)
                if item is None:
                    break

                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                elif item.layout():
                    # Prevent deep recursion
                    if iteration < MAX_RECURSION_DEPTH:  # Limit recursion depth
                        self._clear_layout(item.layout())

                iteration += 1

            if iteration >= max_iterations:
                self._logger.warning(
                    f"Warning: _clear_layout reached maximum iterations ({max_iterations})",
                )

        except Exception as e:
            self._logger.error(f"Error in _clear_layout: {e}")

    def _add_common_properties(self, component_data):
        """Add common properties to the form."""
        # Name field with consistent styling
        name_edit = self._create_form_input(MLineEdit)
        name_edit.setText(component_data.get(PROPERTY_NAME, ""))
        name_edit.textChanged.connect(
            lambda name: self._on_property_changed(PROPERTY_NAME, name),
        )
        self.form_layout.addRow(FORM_LABEL_NAME, name_edit)

        # Visible checkbox with consistent styling
        visible_check = MCheckBox()
        visible_check.setChecked(component_data.get(PROPERTY_VISIBLE, True))
        visible_check.stateChanged.connect(
            lambda state: self._on_property_changed(PROPERTY_VISIBLE, state > 0),
        )
        # Set consistent size for checkbox
        visible_check.setMinimumHeight(FORM_INPUT_HEIGHT)
        self.form_layout.addRow(FORM_LABEL_VISIBLE, visible_check)

        # Add section separator with consistent styling
        separator = self._create_separator()
        self.form_layout.addRow(separator)

    def _add_card_properties(self, component_data):
        """Add card-specific properties to the form."""
        # Width with consistent styling
        width_spin = self._create_form_input(MDoubleSpinBox)
        width_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        width_spin.setValue(component_data.get(PROPERTY_WIDTH, DEFAULT_COMPONENT_WIDTH))
        width_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_WIDTH, value),
        )
        self.form_layout.addRow(FORM_LABEL_WIDTH, width_spin)

        # Height with consistent styling
        height_spin = self._create_form_input(MDoubleSpinBox)
        height_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        height_spin.setValue(
            component_data.get(PROPERTY_HEIGHT, DEFAULT_COMPONENT_HEIGHT),
        )
        height_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_HEIGHT, value),
        )
        self.form_layout.addRow(FORM_LABEL_HEIGHT, height_spin)

    def _add_xgen_properties(self, component_data):
        """Add XGen-specific properties to the form."""
        # Density with consistent styling
        density_spin = self._create_form_input(MSpinBox)
        density_spin.setRange(MIN_XGEN_DENSITY, MAX_XGEN_DENSITY)
        density_spin.setValue(
            component_data.get(PROPERTY_DENSITY, DEFAULT_XGEN_DENSITY),
        )
        density_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_DENSITY, value),
        )
        self.form_layout.addRow(FORM_LABEL_DENSITY, density_spin)

        # Length with consistent styling
        length_spin = self._create_form_input(MDoubleSpinBox)
        length_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        length_spin.setValue(component_data.get(PROPERTY_LENGTH, DEFAULT_XGEN_LENGTH))
        length_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_LENGTH, value),
        )
        self.form_layout.addRow(FORM_LABEL_LENGTH, length_spin)

    def _add_curve_properties(self, component_data):
        """Add curve-specific properties to the form."""
        # Thickness with consistent styling
        thickness_spin = self._create_form_input(MDoubleSpinBox)
        thickness_spin.setRange(MIN_CURVE_THICKNESS, MAX_CURVE_THICKNESS)
        thickness_spin.setValue(
            component_data.get(PROPERTY_THICKNESS, DEFAULT_CURVE_THICKNESS),
        )
        thickness_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_THICKNESS, value),
        )
        self.form_layout.addRow(FORM_LABEL_THICKNESS, thickness_spin)

        # Subdivisions with consistent styling
        subdiv_spin = self._create_form_input(MSpinBox)
        subdiv_spin.setRange(MIN_CURVE_SUBDIVISIONS, MAX_CURVE_SUBDIVISIONS)
        subdiv_spin.setValue(
            component_data.get(PROPERTY_SUBDIVISIONS, DEFAULT_CURVE_SUBDIVISIONS),
        )
        subdiv_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_SUBDIVISIONS, value),
        )
        self.form_layout.addRow(FORM_LABEL_SUBDIVISIONS, subdiv_spin)

    def _on_property_changed(self, prop_name, prop_value):
        """Handle property changes.

        Args:
            prop_name (str): Name of the property that changed
            prop_value: New value of the property
        """
        if not self.current_component:
            return

        # Update the component data
        self.current_component[prop_name] = prop_value

        # Notify the manager about the change (if component has an ID)
        if isinstance(self.current_component, dict) and "id" in self.current_component:
            # For dictionary-based components
            component_id = self.current_component["id"]
            kwargs = {prop_name: prop_value}
            self.manager.update_component(component_id, **kwargs)
        elif hasattr(self.current_component, "id"):
            # For object-based components
            component_id = self.current_component.id
            kwargs = {prop_name: prop_value}
            self.manager.update_component(component_id, **kwargs)
