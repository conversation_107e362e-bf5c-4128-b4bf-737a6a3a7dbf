"""Hair Studio Constants Module.

This module defines all constants used throughout the Hair Studio tool,
including UI text, magic numbers, and configuration values.
"""

HAIR_MATCH_HEAD_NAME = "head_lod0_mesh"

# Hair type constants
HAIR_TYPE_CARD = "card"
HAIR_TYPE_XGEN = "xgen"
HAIR_TYPE_CURVE = "curve"

# ============================================================================
# ASSET CONFIGURATION CONSTANTS - Asset paths and file extensions
# ============================================================================

# CARD TYPE ASSET CONFIGURATION
# ============================================================================
# Card type assets use dynamic path discovery system based on root directories
# and automatic subdirectory matching.
#
# Configuration Steps:
# 1. Set root directories in ALL_HAIR_ASSET_DIR
# 2. Create subdirectories matching tab keys (hair, eyebrow, beard) under each root
# 3. AssetLibConfig will automatically discover and merge paths
#
# Directory Structure Example:
#   U:\hair_lib\card_lib\
#   ├── hair\          # Matches "hair" tab key
#   ├── eyebrow\       # Matches "eyebrow" tab key
#   └── beard\         # Matches "beard" tab key
#
# Flexible Matching Rules:
# - Case insensitive: "Hair", "HAIR", "hair" all match "hair" tab
# - Aliases supported: "eyebrows", "brows" match "eyebrow" tab
# - See AssetLibConfig._is_path_match() for full alias list

# Root directories for card type assets
# Modify these paths to point to your asset directories
PUBLICK_HAIR_CARD_LIB_PATH = r"U:\AvatarFactory\hair_lib\card_lib"  # Public/shared assets
PROJECT_HAIR_CARD_LIB_PATH = (  # Project-specific assets (empty = disabled)r"D:\HUB\character_factory\local_hair_lib_test"
    r"R:\AvatarFactory\hair_lib\card_hairs"
)
LOCAL_HAIR_CARD_LIB_PATH = ""  # Local development assets

# All root directories for card type asset discovery
# Empty strings will be filtered out automatically
ALL_HAIR_ASSET_DIR = [PUBLICK_HAIR_CARD_LIB_PATH, PROJECT_HAIR_CARD_LIB_PATH, LOCAL_HAIR_CARD_LIB_PATH]

# Card type sub-tab configuration
# ============================================================================
# This configuration defines the sub-tabs shown in the card type asset library.
# Each tab key is used for:
# 1. Subdirectory matching in root paths (flexible matching rules apply)
# 2. Asset filtering and categorization
# 3. UI tab display
#
# Configuration Fields:
# - show_text: Display text shown in the UI tab
# - lib_path: Additional static paths (will be merged with discovered paths)
#
# Dynamic Path Discovery:
# AssetLibConfig will automatically populate lib_path by:
# 1. Scanning each directory in ALL_HAIR_ASSET_DIR
# 2. Finding subdirectories that match the tab key (using flexible rules)
# 3. Merging with any pre-configured lib_path entries
# 4. Removing duplicates and invalid paths
#
# Example: If ALL_HAIR_ASSET_DIR contains "U:\hair_lib\card_lib" and that
# directory has subdirectories "hair", "eyebrow", "beard", then:
# - "hair" tab will get path "U:\hair_lib\card_lib\hair"
# - "eyebrow" tab will get path "U:\hair_lib\card_lib\eyebrow"
# - "beard" tab will get path "U:\hair_lib\card_lib\beard"

HAIR_ASSET_LIB_TAB_MAP = {
    "eyebrow": {
        "show_text": "眉毛",
        "lib_path": [],  # Will be populated by dynamic discovery
    },
    "hair": {
        "show_text": "头发",
        "lib_path": [],  # Will be populated by dynamic discovery
    },
    "beard": {
        "show_text": "胡子",
        "lib_path": [],  # Will be populated by dynamic discovery
    },
    "scalp": {
        "show_text": "头皮",
        "lib_path": [],  # Will be populated by dynamic discovery
    },
}
# ============================================================================
# SHADER CONFIGURATION CONSTANTS - Modular shader configuration system
# ============================================================================

# Texture mapping configurations for different shader types
BLINN_TEXTURE_MAP = {
    "color": "_diffuse",
    "transparency": "_opacity",
    "specularColor": "_metallic",
}

DX11_HAIR_TEXTURE_MAP = {
    "HairAlphaTexture": "_opacity",
    "HairRootTexture": "_HairRootMap",
    "HairIDTexture": "_HairIDMap",
    "HairAOTexture": "_ao",
}

# Shader parameter configurations
DX11_HAIR_PARAMS = {
    "UseHairAlphaTexture": True,
    "RootColor": (0.21, 0.14, 0.09),
    "TipColor": (0.275, 0.186, 0.127),
    "Brightness": 1.0,
    "Scatter": 0.65,
    "shadowMultiplier": 0.8,
    "Roughness": 0.6,
}

# Shader configuration templates
SHADER_CONFIGS = {
    "blinn": {
        "shader_type": "blinn",
        "texture_map": BLINN_TEXTURE_MAP,
        "params": {},
        "fx_path": None,
    },
    "dx11_hair": {
        "shader_type": "dx11Shader",
        "texture_map": DX11_HAIR_TEXTURE_MAP,
        "params": DX11_HAIR_PARAMS,
        "fx_path": "HairFurUberShader.fx",
    },
}

# Asset type to shader configuration mapping
SUB_ASSET_SHADER_MAP = {
    "hair": "dx11_hair",
    "eyebrow": {
        "_Base": "blinn",
        "default": "dx11_hair"
    },
    "beard": "dx11_hair",
    "scalp": "blinn",
}

SURFACE_PRO_WRAP_PARAMS = {
    "falloffScale": 5,
    "smoothInfluences": 5,
    "smoothNormals": 5,
    "wrapMode": 1,
}
FAR_PRO_WRAP_PARAMS = {
    "falloffScale": 20,
    "smoothInfluences": 5,
    "smoothNormals": 5,
    "wrapMode": 0,
}
PROXIMITY_WRAP_MODES = {
    "scalp": SURFACE_PRO_WRAP_PARAMS,
    "hair": FAR_PRO_WRAP_PARAMS,
    "eyebrow": SURFACE_PRO_WRAP_PARAMS,
    "beard": SURFACE_PRO_WRAP_PARAMS,
}

HAIR_MESH_GROUP = "hair_mesh_grp"
# Default tab to select when card type asset library is opened
DEFAULT_SUB_TAB_KEY = "hair"

# XGEN TYPE ASSET CONFIGURATION
# ============================================================================
# XGen type assets use the same dynamic discovery system as card type.
# Currently no XGen assets are configured, but the system is ready for expansion.
#
# To enable XGen assets:
# 1. Set root directories in ALL_XGEN_ASSET_DIR
# 2. Create subdirectories matching XGEN_ASSET_LIB_TAB_MAP keys
# 3. Update AssetLibConfig to support XGen type discovery

# Root directories for XGen type assets (currently empty - no XGen support)
PUBLICK_HAIR_XGEN_LIB_PATH = ""  # Public XGen assets (disabled)
PROJECT_HAIR_XGEN_LIB_PATH = ""  # Project XGen assets (disabled)
LOCAL_HAIR_XGEN_LIB_PATH = ""  # Local XGen assets (disabled)

# All root directories for XGen type asset discovery
ALL_XGEN_ASSET_DIR = [PUBLICK_HAIR_XGEN_LIB_PATH, PROJECT_HAIR_XGEN_LIB_PATH, LOCAL_HAIR_XGEN_LIB_PATH]

# XGen type sub-tab configuration (placeholder for future expansion)
XGEN_ASSET_LIB_TAB_MAP = {}

# CURVE TYPE ASSET CONFIGURATION
# ============================================================================
# Curve type assets use the same dynamic discovery system as card type.
# Currently no Curve assets are configured, but the system is ready for expansion.
#
# To enable Curve assets:
# 1. Set root directories in ALL_CURVE_ASSET_DIR
# 2. Create subdirectories matching CURVE_ASSET_LIB_TAB_MAP keys
# 3. Update AssetLibConfig to support Curve type discovery

# Root directories for Curve type assets (currently empty - no Curve support)
PUBLICK_HAIR_CURVE_LIB_PATH = ""  # Public Curve assets (disabled)
PROJECT_HAIR_CURVE_LIB_PATH = ""  # Project Curve assets (disabled)
LOCAL_HAIR_CURVE_LIB_PATH = ""  # Local Curve assets (disabled)

# All root directories for Curve type asset discovery
ALL_CURVE_ASSET_DIR = [PUBLICK_HAIR_CURVE_LIB_PATH, PROJECT_HAIR_CURVE_LIB_PATH, LOCAL_HAIR_CURVE_LIB_PATH]

# Curve type sub-tab configuration (placeholder for future expansion)
CURVE_ASSET_LIB_TAB_MAP = {}

# ============================================================================
# ASSET CONFIGURATION CONSTANTS - END
# ============================================================================

# Environment variable names for overriding default paths
ENV_CARD_PATH = "HAIR_STUDIO_CARD_PATH"
ENV_XGEN_PATH = "HAIR_STUDIO_XGEN_PATH"
ENV_CURVE_PATH = "HAIR_STUDIO_CURVE_PATH"

# Supported file extensions
SUPPORTED_MODEL_EXTENSIONS = {".fbx", ".obj", ".ma", ".mb"}
SUPPORTED_THUMBNAIL_EXTENSIONS = {".jpg", ".jpeg", ".png", ".bmp", ".tiff"}

# ============================================================================
# MAYA API MODULER CONSTANTS - SHADER PATH
# ============================================================================

DEFAULT_SHADER_DIR = r"U:\AvatarFactory\hair_lib\shaders"
HAIR_SHADER_NAME = "HairFurUberShader.fx"

# ============================================================================
# UI SHOW TEXT CONSTANTS
# ============================================================================

# UI text constants
UI_TEXT_HAIR_COMPONENT_LIST = "毛发组件列表"  # "Hair Component List"
UI_TEXT_NO_COMPONENT_SELECTED = "没有毛发组件被选中"  # "No Component Selected"
UI_TEXT_CARD_TAB = "插片"  # card
UI_TEXT_XGEN_TAB = "XGen"
UI_TEXT_CURVE_TAB = "Curve曲线"
UI_TEXT_HAIR_ASSET_LIBRARY = "毛发资产库"  # "Hair Asset Library"
UI_TEXT_SEARCH_PLACEHOLDER = "Search assets..."
UI_TEXT_SETTINGS = "设置"
UI_TEXT_ADD_COMPONENT = "添加毛发组件"
UI_TEXT_REMOVE_COMPONENT = "移除此毛发组件"

# Error messages
ERROR_MSG_FAILED_TO_SETUP_UI = "Failed to set up UI"
ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO = "Failed to initialize Hair Studio UI"
ERROR_MSG_ERROR_CHANGING_TABS = "Error changing tabs"
ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST = "Error updating component list"
ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION = "Error handling component selection"
ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT = "Error setting selected component"
ERROR_MSG_ERROR_CREATING_COMPONENT = "Error creating component"
ERROR_MSG_ERROR_SELECTING_COMPONENT = "Error selecting component"
ERROR_MSG_ERROR_DELETING_COMPONENT = "Error deleting component"
ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY = "Error refreshing asset library"
ERROR_MSG_FAILED_TO_CONNECT_SIGNALS = "Failed to connect signals"
ERROR_MSG_FAILED_TO_CREATE_COMPONENT = "Failed to create component"

# Layout constants (will be updated to use UI_BASE_UNIT after it's defined)
LAYOUT_MARGIN_ZERO = 0
LAYOUT_SPACING_SMALL = 2  # Will be updated to UI_BASE_UNIT // 2
LAYOUT_SPACING_MEDIUM = 5  # Will be updated to UI_BASE_UNIT + 1

# Widget stretch factors
STRETCH_FACTOR_EDITOR_AREA = 4  # 40% width
STRETCH_FACTOR_COMPONENT_LIST = 3  # 30% width
STRETCH_FACTOR_ASSET_LIBRARY = 3  # 30% width

# Minimum width constants for QSplitter components
MINIMUM_WIDTH_EDITOR_AREA = 200  # Minimum width for editor area
MINIMUM_WIDTH_COMPONENT_LIST = 150  # Minimum width for component list
MINIMUM_WIDTH_ASSET_LIBRARY = 400  # Minimum width for asset library

# ============================================================================
# UI STYLE CONSTANTS - Unified styling for consistent appearance
# ============================================================================

# Base unit for responsive sizing (can be scaled based on DPI)
UI_BASE_UNIT = 4  # 4px base unit for consistent scaling

# Font sizes (using relative units)
FONT_SIZE_TITLE = "12pt"  # Main titles (h2)
FONT_SIZE_SUBTITLE = "10pt"  # Subtitles
FONT_SIZE_NORMAL = "9pt"  # Normal text
FONT_SIZE_SMALL = "7pt"  # Small text

scale_factor = 1.5  # scale_factor
margin_scale = 1.0

BUTTON_BASE_FACTOR = 8

COMPONENT_BUTTON_SIZE = int(UI_BASE_UNIT * BUTTON_BASE_FACTOR * scale_factor)  # 70px
COMPONENT_ICON_SIZE = int(
    UI_BASE_UNIT * (BUTTON_BASE_FACTOR - 1) * scale_factor,
)  # 50px

# Button interaction constants
BUTTON_ICON_PADDING_RATIO = 0.2  # 20% padding around icon
BUTTON_MIN_ICON_SIZE = 12  # Minimum icon size in pixels
BUTTON_MIN_SIZE = 24  # Minimum usable button size in pixels

# Visibility button fallback text
VISIBILITY_BUTTON_FALLBACK_VISIBLE = "👁"  # Eye emoji as fallback
VISIBILITY_BUTTON_FALLBACK_HIDDEN = "🚫"  # No entry emoji as fallback

# Calculate precise item height after button and margin are defined
_LAYOUT_MARGIN_SMALL = 2  # Reduced margin to give button more space
COMPONENT_ITEM_HEIGHT = COMPONENT_BUTTON_SIZE + 2 * _LAYOUT_MARGIN_SMALL  # Precise fit

COMPONENT_LIST_ITEM_MARGIN = _LAYOUT_MARGIN_SMALL  # Provide buffer space for Qt internal margins and visual spacing
# Title heights for consistent vertical alignment
TITLE_HEIGHT_STANDARD = UI_BASE_UNIT * 8  # 32px for all title labels
TITLE_PADDING_VERTICAL = UI_BASE_UNIT * 2  # 8px vertical padding

# Asset item sizes
ITEM_SCALE = 1
ASSET_ITEM_WIDTH = UI_BASE_UNIT * 30 * ITEM_SCALE  # 120px
ASSET_ITEM_HEIGHT = UI_BASE_UNIT * 35 * ITEM_SCALE  # 140px
ASSET_THUMBNAIL_SIZE = UI_BASE_UNIT * 25 * ITEM_SCALE  # 100px

# Layout margins and spacing (using the pre-calculated margin)
LAYOUT_MARGIN_SMALL = _LAYOUT_MARGIN_SMALL  # Use pre-calculated value for precise fit
LAYOUT_MARGIN_MEDIUM = LAYOUT_MARGIN_SMALL * 2  # 8px
LAYOUT_MARGIN_LARGE = LAYOUT_MARGIN_SMALL * 3  # 12px
LAYOUT_MARGIN_TINY = LAYOUT_MARGIN_SMALL // 2  # 2px

# Layout spacing (based on margin for consistency)
LAYOUT_SPACING_SMALL = LAYOUT_MARGIN_SMALL // 2  # 2px
LAYOUT_SPACING_MEDIUM = LAYOUT_MARGIN_SMALL + 1  # 5px

# Grid and container spacing
GRID_SPACING_DEFAULT = UI_BASE_UNIT * 2.5  # 10px
GRID_COLUMNS_DEFAULT = 3  # Default number of columns in asset grid (fallback)
GRID_COLUMNS_MIN = 1  # Minimum columns in responsive layout
GRID_COLUMNS_MAX = 8  # Maximum columns in responsive layout

# Responsive layout constants
ASSET_ITEM_MIN_SPACING = UI_BASE_UNIT * 2  # 8px minimum spacing between items
LAYOUT_PADDING_HORIZONTAL = UI_BASE_UNIT * 4  # 16px horizontal padding for calculations

# Proportional spacing constants for improved layout
SPACING_RATIO_DEFAULT = 0.15  # 15% of item width for optimal visual balance
SPACING_MIN_ABSOLUTE = UI_BASE_UNIT * 2  # 8px absolute minimum spacing
SPACING_MAX_ABSOLUTE = UI_BASE_UNIT * 7.5  # 30px absolute maximum spacing

# Mouse wheel zoom constants
WHEEL_ZOOM_MIN_SCALE = 0.5  # Minimum zoom scale factor
WHEEL_ZOOM_MAX_SCALE = 2.0  # Maximum zoom scale factor
WHEEL_ZOOM_STEP = 0.1  # Zoom step per wheel event (10%)
WHEEL_ZOOM_DEBOUNCE_DELAY = 50  # Milliseconds to debounce wheel events
WHEEL_ZOOM_DEFAULT_SCALE = 1.0  # Default zoom scale factor

# Border radius for consistent rounded corners
BORDER_RADIUS_SMALL = UI_BASE_UNIT // 2  # 2px
BORDER_RADIUS_MEDIUM = UI_BASE_UNIT // 2 + 1  # 3px
BORDER_RADIUS_LARGE = UI_BASE_UNIT * 2  # 8px

# Border widths
BORDER_WIDTH_MEDIUM = UI_BASE_UNIT // 2  # 2px

# Color constants for consistent theming - matching Face Sculpting Center
# Primary colors
COLOR_PRIMARY = "#2683d9"  # Blue primary color - matching APP_PRIMARY_COLOR
COLOR_PRIMARY_HOVER = "#3090E0"  # Lighter blue for hover
COLOR_PRIMARY_PRESSED = "#1F70C0"  # Darker blue for pressed

# Background colors
COLOR_BG_TRANSPARENT = "transparent"
COLOR_BG_DARKER = "#1E1E1E"
COLOR_BG_HOVER = "rgba(255, 255, 255, 0.05)"
COLOR_BG_SELECTED = "rgba(64, 128, 255, 0.2)"
COLOR_BG_SELECTED_HOVER = "rgba(64, 128, 255, 0.3)"

# Border colors
COLOR_BORDER_TRANSPARENT = "transparent"
COLOR_BORDER_DEFAULT = "#3E3E3E"
COLOR_BORDER_SELECTED = "#4080FF"

# Component-specific style templates
# Unified Asset Item Component Styles
# Single component-level style management instead of separate sub-element styles
COLOR_ITEM_SELECTED = "#1890FF"

# Component Item styles
COMPONENT_ITEM_STYLE_NORMAL = f"""
ComponentItem {{
    background: {COLOR_BG_TRANSPARENT};
    border: 1px solid {COLOR_BORDER_TRANSPARENT};
    border-radius: {BORDER_RADIUS_SMALL}px;
}}
ComponentItem:hover {{
    background: {COLOR_BG_HOVER};
    border: 1px solid {COLOR_BORDER_DEFAULT};
}}
"""

COMPONENT_ITEM_STYLE_SELECTED = f"""
ComponentItem {{
    background: {COLOR_BG_SELECTED};
    border: 1px solid {COLOR_BORDER_SELECTED};
    border-radius: {BORDER_RADIUS_SMALL}px;
}}
"""


# Subtitle styles for section headers with consistent height
SUBTITLE_STYLE = f"""
QLabel {{
    font-size: {FONT_SIZE_SUBTITLE};
    color: #FFFFFF;
    font-weight: bold;
    min-height: {TITLE_HEIGHT_STANDARD}px;
    max-height: {TITLE_HEIGHT_STANDARD}px;
    padding: {TITLE_PADDING_VERTICAL}px 0px;
}}
"""

# No component selected message style
NO_COMPONENT_STYLE = f"""
QLabel {{
    font-size: {FONT_SIZE_NORMAL};
    color: #888888;
    font-style: italic;
    min-height: {TITLE_HEIGHT_STANDARD}px;
    max-height: {TITLE_HEIGHT_STANDARD}px;
    padding: {TITLE_PADDING_VERTICAL}px 0px;
}}
"""

# Note: TAB_STYLE_H2 has been removed as Hair Studio now uses dayu_theme for tab styling
# Hair Studio tabs inherit styling from dayu_theme.apply() for consistency with Face Sculpting Center

# Form input control styles
FORM_INPUT_STYLE = f"""
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    min-width: {UI_BASE_UNIT * 30}px;
    min-height: {UI_BASE_UNIT * 8}px;
    padding: {UI_BASE_UNIT}px {UI_BASE_UNIT * 2}px;
    border: 1px solid #3E3E3E;
    border-radius: {BORDER_RADIUS_SMALL}px;
    background: #2D2D2D;
    color: #FFFFFF;
    font-size: {FONT_SIZE_NORMAL};
}}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
    border-color: #1890FF;
    background: #353535;
}}

QLineEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
    border-color: #4080FF;
    background: #353535;
}}

QLineEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {{
    background: #1A1A1A;
    color: #666666;
    border-color: #2A2A2A;
}}
"""

# Form label styles
FORM_LABEL_STYLE = f"""
QLabel {{
    font-size: {FONT_SIZE_NORMAL};
    color: #CCCCCC;
    font-weight: normal;
    min-width: {UI_BASE_UNIT * 20}px;
    padding-right: {UI_BASE_UNIT}px;
}}
"""

# Form separator styles
FORM_SEPARATOR_STYLE = """
QFrame[frameShape="4"] {
    color: #3E3E3E;
    background-color: #3E3E3E;
    border: none;
    max-height: 1px;
    margin: 8px 0px;
}
"""

# Form layout spacing and sizing
FORM_SPACING_DEFAULT = LAYOUT_SPACING_MEDIUM
FORM_MARGIN_DEFAULT = LAYOUT_MARGIN_MEDIUM
FORM_LABEL_WIDTH = UI_BASE_UNIT * 20  # 80px - consistent label width
FORM_INPUT_MIN_WIDTH = UI_BASE_UNIT * 30  # 120px - minimum input width
FORM_INPUT_HEIGHT = UI_BASE_UNIT * 8  # 32px - standard input height


# ============================================================================
# BOTTOM AREA CONSTANTS - Unified bottom area height management
# ============================================================================

# Bottom area height constants for consistent visual alignment
BOTTOM_AREA_HEIGHT = UI_BASE_UNIT * 12  # 48px - unified height for all bottom areas
BOTTOM_AREA_PADDING_VERTICAL = UI_BASE_UNIT * 2  # 8px - vertical padding
BOTTOM_AREA_PADDING_HORIZONTAL = LAYOUT_SPACING_MEDIUM  # 5px - horizontal padding
BOTTOM_AREA_SPACING = LAYOUT_SPACING_MEDIUM  # 5px - spacing between elements

# Button area constants (for component_list button_layout)
BUTTON_AREA_HEIGHT = BOTTOM_AREA_HEIGHT  # Consistent with bottom area
BUTTON_AREA_BUTTON_SIZE = UI_BASE_UNIT * 8  # 32px - standard button size
BUTTON_AREA_MARGIN = (BOTTOM_AREA_HEIGHT - BUTTON_AREA_BUTTON_SIZE) // 2  # Center buttons vertically

# Slider area constants (for asset_library slider_layout)
SLIDER_AREA_HEIGHT = BOTTOM_AREA_HEIGHT  # Consistent with bottom area
SLIDER_AREA_SLIDER_WIDTH = UI_BASE_UNIT * 30  # 120px - slider width
SLIDER_AREA_SLIDER_HEIGHT = UI_BASE_UNIT * 5  # 20px - slider height
SLIDER_AREA_MARGIN = (BOTTOM_AREA_HEIGHT - SLIDER_AREA_SLIDER_HEIGHT) // 2  # Center slider vertically

# ============================================================================
# FOCUS CONTROL CONSTANTS - Remove blue focus outline from UI components
# ============================================================================

# Focus policy constants (using Qt constant values)
FOCUS_POLICY_NO_FOCUS = 0x00  # Qt.NoFocus - disable focus for containers
FOCUS_POLICY_TAB_FOCUS = 0x01  # Qt.TabFocus - tab navigation only
FOCUS_POLICY_CLICK_FOCUS = 0x02  # Qt.ClickFocus - click to focus
FOCUS_POLICY_STRONG_FOCUS = 0x0B  # Qt.StrongFocus - tab and click focus

# Focus outline removal styles
FOCUS_OUTLINE_DISABLED_STYLE = """
QScrollArea:focus, QWidget:focus {
    outline: none;
    border: none;
}
QScrollArea {
    border: none;
    outline: none;
}
QWidget {
    outline: none;
}
"""


# Default values
DEFAULT_TAB_INDEX = 0
DEFAULT_COMPONENT_WIDTH = 1.0
DEFAULT_COMPONENT_HEIGHT = 1.0
DEFAULT_XGEN_DENSITY = 1000
DEFAULT_XGEN_LENGTH = 1.0
DEFAULT_CURVE_THICKNESS = 0.1
DEFAULT_CURVE_SUBDIVISIONS = 3

# Range limits
MIN_COMPONENT_SIZE = 0.01
MAX_COMPONENT_SIZE = 100.0
MIN_XGEN_DENSITY = 1
MAX_XGEN_DENSITY = 10000
MIN_CURVE_THICKNESS = 0.001
MAX_CURVE_THICKNESS = 10.0
MIN_CURVE_SUBDIVISIONS = 1
MAX_CURVE_SUBDIVISIONS = 10

# Safety limits
MAX_CLEAR_ITERATIONS = 100
MAX_RECURSION_DEPTH = 10

# Object names
OBJECT_NAME_HAIR_STUDIO_TAB = "HairStudioTab"
OBJECT_NAME_COMPONENT_LIST_FORMAT = "{}_component_list"
OBJECT_NAME_ASSET_LIBRARY_FORMAT = "{}_asset_library"

# Icon names - Using existing icons where available, Qt standard icons as fallback
# Using existing icons from resources/static/images/ where possible
ICON_ADD_LINE = "add_on.svg"  # Using existing add_on.svg
ICON_TRASH_LINE = "trash.svg"  # Using existing trash.svg
ICON_SETTINGS_LINE = "setting.svg"  # Using existing setting.svg
ICON_SEARCH_LINE = "search_line.svg"  # Will fallback to QStyle.SP_FileDialogDetailedView
ICON_EYE_LINE = "eye_line.svg"  # Will fallback to QStyle.SP_DialogApplyButton
ICON_EYE_OFF_LINE = "eye_off_line.svg"  # Will fallback to QStyle.SP_DialogCancelButton
ICON_HAIR_CARD = "card_line.svg"  # Will fallback to QStyle.SP_FileIcon
ICON_HAIR_XGEN = "xgen_line.svg"  # Will fallback to QStyle.SP_DirIcon
ICON_HAIR_CURVE = "curve_line.svg"  # Will fallback to QStyle.SP_FileDialogListView

# Qt Standard Icon mappings for fallback when custom icons are not found
QT_ICON_FALLBACKS = {
    ICON_ADD_LINE: "SP_FileDialogNewFolder",
    ICON_TRASH_LINE: "SP_TrashIcon",
    ICON_SETTINGS_LINE: "SP_ComputerIcon",
    ICON_SEARCH_LINE: "SP_FileDialogDetailedView",
    ICON_EYE_LINE: "SP_DialogApplyButton",
    ICON_EYE_OFF_LINE: "SP_DialogCancelButton",
    ICON_HAIR_CARD: "SP_FileIcon",
    ICON_HAIR_XGEN: "SP_DirIcon",
    ICON_HAIR_CURVE: "SP_FileDialogListView",
}

# Property names
PROPERTY_NAME = "name"
PROPERTY_VISIBLE = "visible"
PROPERTY_WIDTH = "width"
PROPERTY_HEIGHT = "height"
PROPERTY_DENSITY = "density"
PROPERTY_LENGTH = "length"
PROPERTY_THICKNESS = "thickness"
PROPERTY_SUBDIVISIONS = "subdivisions"

# Form labels
FORM_LABEL_NAME = "Name:"
FORM_LABEL_VISIBLE = "Visible:"
FORM_LABEL_WIDTH = "Width:"
FORM_LABEL_HEIGHT = "Height:"
FORM_LABEL_DENSITY = "Density:"
FORM_LABEL_LENGTH = "Length:"
FORM_LABEL_THICKNESS = "Thickness:"
FORM_LABEL_SUBDIVISIONS = "Subdivisions:"

############################################## ICON ########################################
DEFAULT_ICON_SIZE = 32
