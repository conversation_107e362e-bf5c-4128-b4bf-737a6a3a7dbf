"""FBX library"""

# Import built-in modules
import logging
import os

# Import Maya commands using unified import system
from .utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)

# Import local modules
import cgame_avatar_factory.hair_studio.constants as const
from cgame_avatar_factory.hair_studio.maya_api.assign_shader import assign_material_to_mesh

# Import local modules - delayed import to avoid Maya module loading issues
import cgame_avatar_factory.hair_studio.maya_api.surface_wrap_mesh as surface_wrap_mesh
import cgame_avatar_factory.hair_studio.maya_api.utils as maya_utils


def import_asset_by_name(path, group_name):
    """
    Import an asset and parent it under a group.
    Supports both FBX and OBJ file formats with automatic type detection.

    Args:
        path (unicode): Path to the asset file.
        group_name (unicode): Name of the group node to parent imported objects.
    Returns:
        list: List of imported mesh transform short names.
    """
    logger = logging.getLogger(__name__)
    if not os.path.exists(path):
        logger.error("File not exists. path=%s", path)
        return None

    try:
        # Determine asset file type
        asset_type = maya_utils.get_asset_file_type(path)
        if not asset_type:
            logger.error("Unsupported asset file type: %s", path)
            return None

        logger.debug(f"Importing {asset_type} asset: {path}")

        # 1. check group exists
        if cmds.objExists(group_name):
            children = cmds.listRelatives(group_name, c=True, f=True) or []
            if children:
                cmds.delete(children)
            cmds.delete(group_name)
            logger.debug("deleted for already exists group: {0}".format(group_name))

        # 2. Import asset based on type
        if not import_asset_file_by_type(path, group_name, asset_type):
            return None
        # 3. New transforms under group after import
        new_transforms = cmds.listRelatives(
            group_name,
            c=True,
            type="transform",
            f=True,
            ad=1,
        )
        imported = [t for t in new_transforms if cmds.listRelatives(t, c=True, type="mesh")]

        logger.debug(
            "imported mesh transforms in group_name '%s': %s",
            group_name,
            imported,
        )
        imported_asset_names = [cmds.ls(t, sn=True)[0] for t in imported]

        if not imported_asset_names:
            logger.warning("No mesh transform imported from %s", path)
            return None
        # NOTE: now only support one object
        imported_asset_name = imported_asset_names[0]

        # 4. Freeze imported asset
        cmds.delete(imported_asset_name, ch=True)
        cmds.makeIdentity(imported_asset_name, apply=True, t=1, r=1, s=1, n=0)

        # 5. Handle axis alignment if needed
        rotate = maya_utils.get_rotate_by_axis_up(imported_asset_name)
        cmds.setAttr(f"{imported_asset_name}.rotate", rotate[0], rotate[1], rotate[2])
        cmds.makeIdentity(imported_asset_name, apply=True, t=1, r=1, s=1, n=0)

        # 6. Rename imported asset
        # Import local modules
        import cgame_avatar_factory.hair_studio.utils.data_util as data_util
        new_name = cmds.rename(
            imported_asset_name,
            "{0}_{1}_imported".format(
                imported_asset_names[0].split("|")[-1],
                data_util.generate_short_unique_name(),
            ),
        )
        logger.debug(f"Renamed imported asset to: {new_name}")
        return new_name
    except Exception as e:
        logger.error("Failed to import asset: %s", str(e))
        return None


def import_asset_file_by_type(path, group_name, asset_type="FBX"):
    """Import asset file by type."""
    # asset_type is one of "FBX", "OBJ"
    if asset_type not in ["FBX", "OBJ"]:
        raise ValueError(f"Unsupported asset type: {asset_type}")
    try:
        cmds.file(
            path,
            i=True,
            type=asset_type,
            iv=True,
            ra=True,
            mnc=True,
            pr=True,
            itr="combine",
            gr=True,
            groupName=group_name,
        )
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.warning(f"Failed to import {asset_type} asset: {e}")
        return False

    return True


def delete_namespace(namespace):
    """Delete the namespace and its contents."""
    logger = logging.getLogger(__name__)
    # check namespace exists
    if cmds.namespace(exists=namespace):
        # get all objects in namespace
        objects_in_namespace = cmds.ls(namespace + ":*", long=True)
        # delete objects in namespace
        if objects_in_namespace:
            cmds.delete(objects_in_namespace)

        # delete namespace
        cmds.namespace(rm=namespace, f=True)
        logger.info(
            "Namespace '{}' and its contents have been deleted.".format(namespace),
        )
    else:
        logger.warning("Namespace '{}' does not exist.".format(namespace))


def remove_loaded_asset(obj):
    """Remove obj and check the namespace not be used, and deleted it.

    Args:
        obj (str): maya node name
    """
    logger = logging.getLogger(__name__)
    namespace = obj.rsplit(":", 1)[0]

    try:
        cmds.delete(obj)
        # check namespace not be used, and deleted it
        delete_namespace(namespace)
    except Exception as e:
        logger.error("Failed to remove asset: {0}".format(e))
        return False
    return True


class WrapHairMesh:
    def __init__(
        self,
        proxy_head,
        hair_path,
        namespace,
        hair_head_mesh_path,
        texture_path,
        asset_type="hair",
    ):
        # WORKAROUND: Use a different logger name because 'load_maya_asset' seems to be filtered in Maya
        # Original: logging.getLogger(__name__) -> 'cgame_avatar_factory.hair_studio.maya_api.load_maya_asset'
        # Workaround: Use 'hair_asset_loader' instead
        logger_name = __name__.replace("load_maya_asset", "hair_asset_loader")
        self._logger = logging.getLogger(logger_name)

        # Ensure basic configuration
        self._logger.propagate = True

        self._logger.debug(
            "Loading proxy_head= {0}, hair asset: {1}, namespace: {2}, hair_head_mesh_path: {3}, texture_path: {4},"
            " asset_type: {5}".format(
                proxy_head,
                hair_path,
                namespace,
                hair_head_mesh_path,
                texture_path,
                asset_type,
            ),
        )

        # Store parameters for potential future use
        self.proxy_head = proxy_head
        self.hair_path = hair_path
        self.namespace = namespace
        self.hair_head_mesh_path = hair_head_mesh_path
        self.texture_path = texture_path

        self.proxy_head = proxy_head
        if not cmds.objExists(self.proxy_head):
            self._logger.error("Proxy head not exists: {0}".format(self.proxy_head))
            return

        if not self.load_all_asset(hair_path, hair_head_mesh_path, namespace):
            self._logger.error("Failed to load asset for hair_path %s", hair_path)
            return

        # NOTE: wrap hair to surface first!
        proximity_wrap_parm = const.PROXIMITY_WRAP_MODES.get(asset_type, const.SURFACE_PRO_WRAP_PARAMS)
        self.wrap_node_name = surface_wrap_mesh.createProximityWrap(
            self.hair_head_mesh,
            [self.hair_target],
            "{0}_wrap".format(self.hair_target),
            proximity_wrap_parm,
        )

        # NOTE: make sure real head change will update to hair base head mesh;
        self.bs_node = self.create_blendshape(
            "{0}_{1}_bs".format(namespace, self.hair_head_mesh),
            self.proxy_head,
            self.hair_head_mesh,
        )

        
        self.shader_node, self.restore_all_nodes = assign_material_to_mesh(
            self.hair_target,
            texture_path,
            shader_name="{0}_{1}_shader".format(namespace, self.hair_target),
            asset_type=asset_type,
        )

    def load_all_asset(self, hair_path, hair_head_mesh_path, namespace):
        # NOTE: load asset
        self.hair_target = import_asset_by_name(hair_path, namespace)
        self.hair_head_mesh = import_asset_by_name(
            hair_head_mesh_path,
            "head_{}".format(namespace),
        )
        if not self.hair_target or not self.hair_head_mesh:
            self._logger.error("Failed to load asset for category %s", namespace)
            return False
        # NOTE: group maya node
        try:
            par = const.HAIR_MESH_GROUP
            if not cmds.objExists(par):
                par = cmds.group(empty=True, name=par)
            reparent_objs = [self.hair_target, self.hair_head_mesh]
            for obj in reparent_objs:
                old_grp = cmds.listRelatives(obj, p=True)
                cmds.parent(old_grp, par, relative=True)

        except Exception as e:
            self._logger.warning("Failed to group maya node: {0}", str(e))

        return True

    def unload_all_created(self):
        # NOTE: remove wrap
        try:
            if hasattr(self, "bs_node") and self.bs_node:
                cmds.delete(self.bs_node)
            if hasattr(self, "wrap_node_name") and self.wrap_node_name:
                cmds.delete(self.wrap_node_name)

            # NOTE: deleta shader node
            # cmds.delete(self.restore_all_nodes)

        except Exception as e:
            self._logger.error("Failed to remove wrap: {0}".format(str(e)))
            return False
        success = True
        if self.hair_head_mesh:
            success = success and remove_loaded_asset(self.hair_head_mesh)
        if self.hair_target:
            success = success and remove_loaded_asset(self.hair_target)
        return success

    def create_blendshape(self, name, proxy_head, hair_head_mesh):
        """Create a blendshape node between proxy head and hair head mesh.

        Args:
            name (str): Name of the blendshape node.
            proxy_head (str): Name of the proxy head mesh.
            hair_head_mesh (str): Name of the hair head mesh.

        Returns:
            str: Name of the created blendshape node.
        """
        if not proxy_head or not cmds.objExists(proxy_head):
            self._logger.warning("Proxy head not exists: {0}".format(proxy_head))
            return None
        node = cmds.blendShape(proxy_head, hair_head_mesh, name=name)[0]
        cmds.blendShape(node, e=1, w=[(0, 1.0)])
        cmds.hide(hair_head_mesh)
        return node

    def get_export_nodes(self):
        """Get the list of nodes that should be exported.

        Returns:
            list: List of node names to be exported.
        """
        export_nodes = []

        supported_node_types = ["transform", "dx11Shader", "lambert"]

        # Add hair target mesh
        if hasattr(self, "hair_target") and self.hair_target and cmds.objExists(self.hair_target):
            export_nodes.append(self.hair_target)

            # Get all child nodes of hair target
            children = (
                cmds.listRelatives(
                    self.hair_target,
                    allDescendents=True,
                    fullPath=False,
                )
                or []
            )
            children = [child for child in children if cmds.nodeType(child) in supported_node_types]
            export_nodes.extend(children)

        # Add related material nodes
        if hasattr(self, "shader_node") and self.shader_node:
            if isinstance(self.shader_node, list):
                export_nodes.extend(self.shader_node)
            else:
                export_nodes.append(self.shader_node)

        # Filter out non-existent nodes
        export_nodes = [node for node in export_nodes if cmds.objExists(node)]

        self._logger.debug(f"Export nodes for hair asset: {export_nodes}")
        return export_nodes
