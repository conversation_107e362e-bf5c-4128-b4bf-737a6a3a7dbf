# Import built-in modules
import os

# Import third-party modules
from qtpy import Qt<PERSON>ore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.common.constants as const
from cgame_avatar_factory.common.utils import custom_topo_processing as topo_utils
from cgame_avatar_factory.common.utils import utils
from cgame_avatar_factory.face_sculpting_center.ui.parametric_parts.parametric_eyes_widget import ParametricEyesWidget


class JsonTooltip(QtWidgets.QLabel):
    _current_tooltip = None

    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.setText(text)
        self.setWindowFlags(QtCore.Qt.ToolTip | QtCore.Qt.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WA_ShowWithoutActivating)
        self.setStyleSheet(
            f"""
            QLabel {{
                background-color: {const.DAYU_BG_COLOR};
                border: 2px solid {const.BUTTON_BORDER};
                border-radius: 6px;
                color: {const.DAYU_PRIMARY_TEXT_COLOR};
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                max-width: 400px;
                line-height: 1.4;
            }}
        """
        )
        self.setWordWrap(True)
        self.setMargin(8)

        self.hide_timer = QtCore.QTimer()
        self.hide_timer.timeout.connect(self.hide)
        self.hide_timer.setSingleShot(True)

        if parent:
            self._install_global_filter()

    def _install_global_filter(self):
        app = QtWidgets.QApplication.instance()
        if app:
            app.installEventFilter(self)

    def _remove_global_filter(self):
        app = QtWidgets.QApplication.instance()
        if app:
            app.removeEventFilter(self)

    def eventFilter(self, obj, event):
        if event.type() == QtCore.QEvent.MouseButtonPress:
            if event.button() == QtCore.Qt.LeftButton:
                if not self.geometry().contains(event.globalPos()):
                    self.hide()
        return super().eventFilter(obj, event)

    @classmethod
    def hide_current_tooltip(cls):
        if cls._current_tooltip and cls._current_tooltip.isVisible():
            cls._current_tooltip.hide()

    def show_at_position(self, global_pos):
        self.hide_current_tooltip()

        JsonTooltip._current_tooltip = self

        self.adjustSize()

        screen = QtWidgets.QApplication.primaryScreen()
        screen_rect = screen.availableGeometry()

        x = global_pos.x() + 10
        y = global_pos.y() + 10

        if x + self.width() > screen_rect.right():
            x = global_pos.x() - self.width() - 10

        if y + self.height() > screen_rect.bottom():
            y = global_pos.y() - self.height() - 10

        x = max(screen_rect.left(), x)
        y = max(screen_rect.top(), y)

        self.move(x, y)
        self.show()
        self.raise_()

        self.hide_timer.start(3000)

    def enterEvent(self, event):
        self.hide_timer.stop()
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.hide_timer.start(1000)
        super().leaveEvent(event)

    def hide(self):
        super().hide()
        self.hide_timer.stop()
        self._remove_global_filter()

        if JsonTooltip._current_tooltip == self:
            JsonTooltip._current_tooltip = None


class DraggableIconWidget(QtWidgets.QWidget):
    ICON_SIZE = 80
    WIDGET_SIZE = (100, 120)

    def __init__(self, icon_path, parent=None):
        super().__init__(parent)
        self.icon_path = icon_path
        self.setFixedSize(*self.WIDGET_SIZE)
        self._setup_ui()
        self._load_icon()

    def _setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        self.icon_label = QtWidgets.QLabel()
        self.icon_label.setFixedSize(self.ICON_SIZE, self.ICON_SIZE)
        self.icon_label.setAlignment(QtCore.Qt.AlignCenter)
        self.icon_label.setStyleSheet(
            f"""
            QLabel {{
                border: 2px solid {const.BUTTON_BORDER};
                border-radius: 4px;
                background-color: {const.UI_COLOR_BG_DARKER};
            }}
        """
        )

        filename = os.path.splitext(os.path.basename(self.icon_path))[0]
        self.name_label = QtWidgets.QLabel(filename)
        self.name_label.setAlignment(QtCore.Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setStyleSheet(
            f"""
            QLabel {{
                color: {const.DAYU_SECONDARY_TEXT_COLOR};
                font-size: 14px;
                background: transparent;
                border: none;
            }}
        """
        )

        layout.addWidget(self.icon_label)
        layout.addWidget(self.name_label)

        self.setStyleSheet(
            f"""
            DraggableIconWidget:hover {{
                background-color: {const.BUTTON_BG};
                border-radius: 4px;
            }}
        """
        )

    def _load_icon(self):
        pixmap = QtGui.QPixmap(self.icon_path)
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                self.ICON_SIZE - 4,
                self.ICON_SIZE - 4,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.icon_label.setPixmap(scaled_pixmap)
        else:
            self.icon_label.setText("无图标")

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.drag_start_position = event.pos()
        elif event.button() == QtCore.Qt.RightButton:
            self._show_info_menu(event.pos())

    def mouseMoveEvent(self, event):
        if not (event.buttons() & QtCore.Qt.LeftButton):
            return

        if (event.pos() - self.drag_start_position).manhattanLength() < QtWidgets.QApplication.startDragDistance():
            return

        self._start_drag()

    def _start_drag(self):
        drag = QtGui.QDrag(self)
        mimeData = QtCore.QMimeData()
        mimeData.setText(self.icon_path)
        drag.setMimeData(mimeData)

        pixmap = self._get_drag_pixmap()
        if pixmap:
            drag.setPixmap(pixmap)

        drag.exec_(QtCore.Qt.CopyAction)

    def _get_drag_pixmap(self):
        if self.icon_label.pixmap() and not self.icon_label.pixmap().isNull():
            return self.icon_label.pixmap()

        pixmap = QtGui.QPixmap(self.icon_path)
        if not pixmap.isNull():
            return pixmap.scaled(
                self.ICON_SIZE - 4,
                self.ICON_SIZE - 4,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
        return None

    def _show_info_menu(self, position):
        json_path = self._get_json_path()
        json_data = utils.read_json(json_path)

        if json_data:
            self._show_json_tooltip(json_data, position)
        else:
            self._show_no_info_tooltip(position)

    def _get_json_path(self):
        base_name = os.path.splitext(self.icon_path)[0]
        return f"{base_name}.json"

    def _load_json_data(self, json_path):
        if os.path.exists(json_path):
            with open(json_path, "r", encoding="utf-8") as f:
                return json.load(f)
        return None

    def _show_json_tooltip(self, json_data, position):
        formatted_text = self._format_json_for_tooltip(json_data)

        tooltip = JsonTooltip(formatted_text, self)

        global_pos = self.mapToGlobal(position)
        tooltip.show_at_position(global_pos)

    def _show_no_info_tooltip(self, position):
        tooltip = JsonTooltip("未找到对应的JSON信息文件", self)
        global_pos = self.mapToGlobal(position)
        tooltip.show_at_position(global_pos)

    def _format_json_for_tooltip(self, json_data):
        if isinstance(json_data, dict):
            lines = []
            for key, value in json_data["data"].items():
                if isinstance(value, (str, int, float, bool)):
                    lines.append(f"{key}: {value}")
                elif isinstance(value, list):
                    lines.append(f"{key}: [{len(value)} items]")
                elif isinstance(value, dict):
                    lines.append(f"{key}: {{{len(value)} keys}}")
                else:
                    lines.append(f"{key}: {type(value).__name__}")
            return "\n".join(lines[:10])
        else:
            return str(json_data)


class DropCanvasWidget(QtWidgets.QLabel):
    icon_dropped = QtCore.Signal(str)
    icon_deleted = QtCore.Signal()

    MIN_SIZE = (200, 200)
    PLACEHOLDER_TEXT = "拖拽图标到此处"
    PLACEHOLDER_TEXT_NO_RING = "融合区域角色未创建成功"
    ERROR_TEXT = "图标加载失败"

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_icon_path = None
        self._setup_ui()

    def _setup_ui(self):
        self.setAcceptDrops(True)
        self.setMinimumSize(*self.MIN_SIZE)
        self.setAlignment(QtCore.Qt.AlignCenter)
        self._update_placeholder_text()
        self._apply_default_style()

    def _update_placeholder_text(self):
        text = self.PLACEHOLDER_TEXT if self._check_ring_component_count() else self.PLACEHOLDER_TEXT_NO_RING
        self.setText(text)

    def refresh_placeholder_text(self):
        if not self.current_icon_path:
            self._update_placeholder_text()

    def _apply_style(self, border_style="dashed", border_color=None, text_color=None):
        border_color = border_color or const.BUTTON_BORDER
        text_color = text_color or const.APP_PRIMARY_COLOR
        self.setStyleSheet(
            f"""
            QLabel {{
                border: 2px {border_style} {border_color};
                border-radius: 4px;
                background-color: {const.DAYU_BG_COLOR};
                color: {text_color};
            }}
        """
        )

    def _apply_default_style(self):
        self._apply_style()

    def dragEnterEvent(self, event):
        if self.current_icon_path:
            event.ignore()
            return

        if event.mimeData().hasText():
            event.acceptProposedAction()
            self._apply_style(border_color=const.BUTTON_HOVER_BG, text_color=const.DAYU_SECONDARY_TEXT_COLOR)

    def dragLeaveEvent(self, event):
        self._apply_default_style()

    def dropEvent(self, event):
        if not self._check_ring_component_count():
            event.ignore()
            return

        if self.current_icon_path:
            event.ignore()
            return

        self.current_icon_path = event.mimeData().text()

        if self._load_dropped_icon(self.current_icon_path):
            self._apply_loaded_style()
            QtWidgets.QApplication.processEvents()
            QtCore.QTimer.singleShot(100, lambda: self.icon_dropped.emit(self.current_icon_path))
        else:
            self.setText(self.ERROR_TEXT)
            self._apply_default_style()

        event.acceptProposedAction()

    def _check_ring_component_count(self):
        parent = self.parent()
        while parent:
            if hasattr(parent, "base_merge_page") and hasattr(parent.base_merge_page, "ring"):
                return len(getattr(parent.base_merge_page.ring, "components", [])) == 4
            parent = parent.parent()
        return False

    def _load_dropped_icon(self, icon_path):
        pixmap = QtGui.QPixmap(icon_path)
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(180, 180, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setText("")
            return True
        return False

    def _apply_loaded_style(self):
        self._apply_style(border_style="solid")

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.RightButton and self.current_icon_path:
            self._show_context_menu(event.pos())
        else:
            super().mousePressEvent(event)

    def _show_context_menu(self, position):
        context_menu = QtWidgets.QMenu(self)
        delete_action = context_menu.addAction("删除图标")
        delete_action.triggered.connect(self.delete_icon)
        context_menu.exec_(self.mapToGlobal(position))

    def delete_icon(self):
        self.current_icon_path = None
        self.clear()
        self.setText(self.PLACEHOLDER_TEXT)
        self._apply_default_style()
        self.icon_deleted.emit()

    def set_icon(self, icon_path):
        if not icon_path or not os.path.exists(icon_path):
            return

        try:
            pixmap = QtGui.QPixmap(icon_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(
                    150,
                    150,
                    QtCore.Qt.KeepAspectRatio,
                    QtCore.Qt.SmoothTransformation,
                )
                self.setPixmap(scaled_pixmap)
                self.current_icon_path = icon_path
                self._apply_loaded_style()
            else:
                self.setText(self.ERROR_TEXT)
                self._apply_default_style()
        except Exception as e:
            self.setText(self.ERROR_TEXT)
            self._apply_default_style()


class ModelWrapWidget(QtWidgets.QWidget):
    FRAME_STYLE = f"""
        QFrame {{
            border: 2px solid {const.BUTTON_HOVER_BG};
            border-radius: 8px;
            background-color: {const.UI_COLOR_BG_DARKER};
        }}
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self._load_saved_icon_path()

    def setup_ui(self):
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        left_frame = self._create_lod_frame()
        right_frame = self._create_wrap_frame()

        main_layout.addWidget(left_frame)
        main_layout.addWidget(right_frame)
        main_layout.setStretchFactor(left_frame, 4)
        main_layout.setStretchFactor(right_frame, 2)

    def _create_lod_frame(self):
        frame = QtWidgets.QFrame()
        frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        frame.setStyleSheet(self.FRAME_STYLE)

        layout = QtWidgets.QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)

        title = ParametricEyesWidget().create_section_title("拓扑模板库")
        layout.addWidget(title)

        scroll_area = self._create_scroll_area()
        layout.addWidget(scroll_area)

        return frame

    def _create_scroll_area(self):
        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)

        icon_container = QtWidgets.QWidget()
        self.icon_layout = QtWidgets.QGridLayout(icon_container)
        self.icon_layout.setSpacing(5)

        scroll_area.setWidget(icon_container)
        self._load_icons()

        return scroll_area

    def _create_wrap_frame(self):
        frame = QtWidgets.QFrame()
        frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        frame.setStyleSheet(self.FRAME_STYLE)

        layout = QtWidgets.QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)

        title = ParametricEyesWidget().create_section_title("创建Wrap")
        layout.addWidget(title)

        self.drop_canvas = DropCanvasWidget()
        self.drop_canvas.icon_dropped.connect(self._on_icon_dropped)
        layout.addWidget(self.drop_canvas)

        return frame

    def _load_saved_icon_path(self):
        saved_icon_path = topo_utils.get_dna_path_from_info_node()
        if saved_icon_path and os.path.exists(saved_icon_path) and hasattr(self, "drop_canvas"):
            self.drop_canvas.set_icon(saved_icon_path)

    def _load_icons(self):
        icon_path = os.path.join(const.CONFIG_PATH, "in_game_topo")

        if not os.path.exists(icon_path):
            return

        png_files = self._get_all_png_files(icon_path)
        if not png_files:
            return

        self._create_icon_grid_from_paths(png_files)

    def _get_all_png_files(self, root_path):
        png_files = []
        for root, _, files in os.walk(root_path):
            for file in files:
                if file.lower().endswith(".png"):
                    full_path = os.path.join(root, file)
                    png_files.append(full_path)

        png_files.sort(key=lambda x: os.path.basename(x).lower())
        return png_files

    def _create_icon_grid_from_paths(self, png_file_paths):
        max_cols = 3
        for i, full_path in enumerate(png_file_paths):
            row, col = divmod(i, max_cols)
            icon_widget = DraggableIconWidget(full_path)
            self.icon_layout.addWidget(icon_widget, row, col)

    def _on_icon_dropped(self, icon_path):
        topo_utils.build_custom_topology(icon_path)
