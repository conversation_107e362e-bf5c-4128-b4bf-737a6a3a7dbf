#!/usr/bin/env python
"""Validation script for shader optimization."""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_config_system():
    """Test the shader configuration system."""
    print("=== 测试配置系统 ===")
    
    try:
        from cgame_avatar_factory.hair_studio.config.shader_configs import (
            get_shader_config, 
            ASSET_SHADER_MAPPING,
            DEFAULT_SHADER_CONFIGS
        )
        print("✓ 配置模块导入成功")
        
        # Test getting configurations
        blinn_config = get_shader_config('blinn')
        print(f"✓ Blinn配置: {blinn_config['shader_type']}")
        
        hair_config = get_shader_config('dx11_hair')
        print(f"✓ Hair配置: {hair_config['shader_type']}")
        
        # Test asset mapping
        print(f"✓ Asset映射数量: {len(ASSET_SHADER_MAPPING)}")
        print(f"✓ 默认配置数量: {len(DEFAULT_SHADER_CONFIGS)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shader_manager():
    """Test the shader config manager."""
    print("\n=== 测试Shader管理器 ===")
    
    try:
        # Mock Maya commands for testing
        class MockCmds:
            def objExists(self, name):
                return False
            def shadingNode(self, type, **kwargs):
                return f"mock_{type}_node"
            def attributeQuery(self, attr, **kwargs):
                return True
            def setAttr(self, attr, value, **kwargs):
                pass
            def pluginInfo(self, plugin, **kwargs):
                return True
            def loadPlugin(self, plugin):
                pass
        
        # Patch the cmds import
        import cgame_avatar_factory.hair_studio.maya_api.assign_shader as assign_shader
        assign_shader.cmds = MockCmds()
        
        from cgame_avatar_factory.hair_studio.maya_api.assign_shader import shader_config_manager
        
        # Test config resolution
        config = shader_config_manager.get_shader_config("blinn")
        print(f"✓ 获取Blinn配置: {config['shader_type']}")
        
        # Test asset type resolution
        config_key = shader_config_manager.resolve_asset_shader_config("hair")
        print(f"✓ Hair资产配置: {config_key}")
        
        config_key = shader_config_manager.resolve_asset_shader_config("eyebrow", "test_Base_material")
        print(f"✓ Eyebrow Base配置: {config_key}")
        
        return True
        
    except Exception as e:
        print(f"✗ Shader管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assign_shader_api():
    """Test the assign shader API."""
    print("\n=== 测试Assign Shader API ===")
    
    try:
        # Mock Maya commands
        class MockCmds:
            def objExists(self, name):
                return True
            def shadingNode(self, type, **kwargs):
                return f"mock_{type}_node"
            def attributeQuery(self, attr, **kwargs):
                return True
            def setAttr(self, attr, value, **kwargs):
                pass
            def pluginInfo(self, plugin, **kwargs):
                return True
            def loadPlugin(self, plugin):
                pass
            def sets(self, *args, **kwargs):
                return "mock_shading_group"
            def connectAttr(self, src, dst, **kwargs):
                pass
            def listConnections(self, attr, **kwargs):
                return None
        
        import cgame_avatar_factory.hair_studio.maya_api.assign_shader as assign_shader
        assign_shader.cmds = MockCmds()
        
        from cgame_avatar_factory.hair_studio.maya_api.assign_shader import (
            create_shader_from_config,
            assign_material_to_mesh
        )
        
        # Test shader creation from config
        created_nodes, shader_node = create_shader_from_config("blinn", "test_shader", None)
        print(f"✓ 从配置创建shader: {shader_node}")
        
        # Test material assignment (mock texture path)
        shader_name, created_nodes = assign_material_to_mesh(
            mesh_name="test_mesh",
            texture_path=None,  # No texture path for testing
            asset_type="hair"
        )
        print(f"✓ 分配材质到mesh: {shader_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Assign Shader API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all validation tests."""
    print("开始验证Shader系统优化...")
    
    tests = [
        test_config_system,
        test_shader_manager,
        test_assign_shader_api,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！优化成功。")
        return 0
    else:
        print("✗ 部分测试失败，需要检查。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
