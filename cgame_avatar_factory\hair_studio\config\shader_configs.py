"""Shader Configuration Module.

This module provides centralized shader configuration management for the Hair Studio.
It defines shader templates, parameter sets, and asset-specific mappings.
"""

import os
from typing import Dict, Any

# Import constants for paths
import cgame_avatar_factory.hair_studio.constants as const


class ShaderConfigRegistry:
    """Registry for shader configurations with validation and extension support."""

    def __init__(self):
        self._configs = {}
        # Load default configs after they are defined
        self._load_default_configs()

    def _load_default_configs(self):
        """Load default shader configurations."""
        # This will be called after DEFAULT_SHADER_CONFIGS is defined
        self._configs.update(DEFAULT_SHADER_CONFIGS)
    
    def register_config(self, key: str, config: Dict[str, Any]):
        """Register a new shader configuration.
        
        Args:
            key: Configuration key
            config: Shader configuration dictionary
        """
        self._validate_config(config)
        self._configs[key] = config
    
    def get_config(self, key: str) -> Dict[str, Any]:
        """Get shader configuration by key.
        
        Args:
            key: Configuration key
            
        Returns:
            Shader configuration dictionary
        """
        if key not in self._configs:
            raise KeyError(f"Unknown shader config: {key}")
        
        config = self._configs[key].copy()
        
        # Build full fx path if needed
        if config.get("fx_path") and not os.path.isabs(config["fx_path"]):
            config["fx_path"] = os.path.join(const.DEFAULT_SHADER_DIR, config["fx_path"])
            
        return config
    
    def list_configs(self) -> list:
        """List all available configuration keys."""
        return list(self._configs.keys())
    
    def _validate_config(self, config: Dict[str, Any]):
        """Validate shader configuration structure."""
        required_keys = ["shader_type", "texture_map", "params"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required key in shader config: {key}")


# Texture mapping configurations
BLINN_TEXTURE_MAP = {
    "color": "_diffuse",
    "transparency": "_opacity", 
    "specularColor": "_metallic",
}

DX11_HAIR_TEXTURE_MAP = {
    "HairAlphaTexture": "_opacity",
    "HairRootTexture": "_HairRootMap",
    "HairIDTexture": "_HairIDMap",
    "HairAOTexture": "_ao",
}

DX11_SKIN_TEXTURE_MAP = {
    "DiffuseTexture": "_diffuse",
    "NormalTexture": "_normal",
    "SpecularTexture": "_specular",
    "OpacityTexture": "_opacity",
}

# Shader parameter configurations
DX11_HAIR_PARAMS = {
    "UseHairAlphaTexture": True,
    "RootColor": (0.21, 0.14, 0.09),
    "TipColor": (0.275, 0.186, 0.127),
    "Brightness": 1.0,
    "Scatter": 0.65,
    "shadowMultiplier": 0.8,
    "Roughness": 0.6,
}

DX11_SKIN_PARAMS = {
    "SubsurfaceScattering": 0.5,
    "Roughness": 0.3,
    "Metallic": 0.0,
    "Specular": 0.5,
}

BLINN_PARAMS = {
    "specularRollOff": 0.7,
    "eccentricity": 0.3,
    "specularColor": (1.0, 1.0, 1.0),
}

# Default shader configurations
DEFAULT_SHADER_CONFIGS = {
    "blinn": {
        "shader_type": "blinn",
        "texture_map": BLINN_TEXTURE_MAP,
        "params": BLINN_PARAMS,
        "fx_path": None,
        "description": "Standard Blinn material for basic surfaces",
    },
    "dx11_hair": {
        "shader_type": "dx11Shader", 
        "texture_map": DX11_HAIR_TEXTURE_MAP,
        "params": DX11_HAIR_PARAMS,
        "fx_path": "HairFurUberShader.fx",
        "description": "DirectX 11 hair shader with advanced hair rendering",
    },
    "dx11_skin": {
        "shader_type": "dx11Shader",
        "texture_map": DX11_SKIN_TEXTURE_MAP,
        "params": DX11_SKIN_PARAMS,
        "fx_path": "SkinShader.fx",
        "description": "DirectX 11 skin shader with subsurface scattering",
    },
}

# Asset type to shader configuration mapping
ASSET_SHADER_MAPPING = {
    "hair": "dx11_hair",
    "eyebrow": {
        "_Base": "blinn", 
        "default": "dx11_hair"
    },
    "beard": "dx11_hair",
    "scalp": "blinn",
    "skin": "dx11_skin",
}

# Specialized parameter overrides for specific use cases
PARAMETER_OVERRIDES = {
    "eyebrow_fine": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.8,
            "Scatter": 0.3,
        }
    },
    "beard_coarse": {
        "base_config": "dx11_hair", 
        "overrides": {
            "Roughness": 0.9,
            "RootColor": (0.15, 0.10, 0.05),
            "TipColor": (0.20, 0.15, 0.10),
        }
    },
}

# Global registry instance - will be initialized after DEFAULT_SHADER_CONFIGS is defined
shader_registry = None


def _get_shader_registry():
    """Get or create the global shader registry."""
    global shader_registry
    if shader_registry is None:
        shader_registry = ShaderConfigRegistry()
    return shader_registry


def get_shader_config(key: str) -> Dict[str, Any]:
    """Get shader configuration by key.

    Args:
        key: Configuration key

    Returns:
        Shader configuration dictionary
    """
    return _get_shader_registry().get_config(key)


def register_shader_config(key: str, config: Dict[str, Any]):
    """Register a new shader configuration.

    Args:
        key: Configuration key
        config: Shader configuration dictionary
    """
    _get_shader_registry().register_config(key, config)


def create_config_with_overrides(base_key: str, overrides: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new configuration by applying overrides to a base configuration.
    
    Args:
        base_key: Base configuration key
        overrides: Parameter overrides to apply
        
    Returns:
        New configuration with overrides applied
    """
    base_config = get_shader_config(base_key)
    new_config = base_config.copy()
    
    # Apply parameter overrides
    if "params" in overrides:
        new_config["params"] = base_config["params"].copy()
        new_config["params"].update(overrides["params"])
    
    # Apply other overrides
    for key, value in overrides.items():
        if key != "params":
            new_config[key] = value
    
    return new_config
