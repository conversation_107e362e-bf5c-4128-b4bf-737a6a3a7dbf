"""Shader Configuration Module.

This module provides centralized shader configuration management for the Hair Studio.
It uses configurations defined in constants.py and provides a unified interface.

配置系统结构说明:
1. constants.py 中定义所有基础配置常量
2. 本模块提供配置管理和扩展功能
3. assign_shader.py 通过本模块访问配置
"""

import os
from typing import Dict, Any

# Import constants for shader configurations
import cgame_avatar_factory.hair_studio.constants as const


class ShaderConfigRegistry:
    """Shader配置注册器，提供配置验证和扩展支持."""

    def __init__(self):
        self._configs = {}
        self._load_default_configs()

    def _load_default_configs(self):
        """从constants.py加载默认shader配置."""
        # 使用constants.py中的SHADER_CONFIGS
        self._configs.update(const.SHADER_CONFIGS)
    
    def register_config(self, key: str, config: Dict[str, Any]):
        """Register a new shader configuration.
        
        Args:
            key: Configuration key
            config: Shader configuration dictionary
        """
        self._validate_config(config)
        self._configs[key] = config
    
    def get_config(self, key: str) -> Dict[str, Any]:
        """Get shader configuration by key.
        
        Args:
            key: Configuration key
            
        Returns:
            Shader configuration dictionary
        """
        if key not in self._configs:
            raise KeyError(f"Unknown shader config: {key}")
        
        config = self._configs[key].copy()
        
        # Build full fx path if needed
        if config.get("fx_path") and not os.path.isabs(config["fx_path"]):
            config["fx_path"] = os.path.join(const.DEFAULT_SHADER_DIR, config["fx_path"])
            
        return config
    
    def list_configs(self) -> list:
        """List all available configuration keys."""
        return list(self._configs.keys())
    
    def _validate_config(self, config: Dict[str, Any]):
        """Validate shader configuration structure."""
        required_keys = ["shader_type", "texture_map", "params"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required key in shader config: {key}")


# ============================================================================
# 配置常量引用 - 直接使用constants.py中的定义，避免重复
# ============================================================================

# 从constants.py导入配置映射
ASSET_SHADER_MAPPING = const.SUB_ASSET_SHADER_MAP

# 参数覆盖配置示例 - 用于创建shader变体
PARAMETER_OVERRIDES = {
    "eyebrow_fine": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.8,
            "Scatter": 0.3,
        }
    },
    "beard_coarse": {
        "base_config": "dx11_hair",
        "overrides": {
            "Roughness": 0.9,
            "RootColor": (0.15, 0.10, 0.05),
            "TipColor": (0.20, 0.15, 0.10),
        }
    },
}

# Global registry instance - will be initialized after DEFAULT_SHADER_CONFIGS is defined
shader_registry = None


def _get_shader_registry():
    """Get or create the global shader registry."""
    global shader_registry
    if shader_registry is None:
        shader_registry = ShaderConfigRegistry()
    return shader_registry


def get_shader_config(key: str) -> Dict[str, Any]:
    """Get shader configuration by key.

    Args:
        key: Configuration key

    Returns:
        Shader configuration dictionary
    """
    return _get_shader_registry().get_config(key)


def register_shader_config(key: str, config: Dict[str, Any]):
    """Register a new shader configuration.

    Args:
        key: Configuration key
        config: Shader configuration dictionary
    """
    _get_shader_registry().register_config(key, config)


def create_config_with_overrides(base_key: str, overrides: Dict[str, Any]) -> Dict[str, Any]:
    """基于现有配置创建变体配置，用于特殊需求的shader定制.

    使用场景:
    1. 创建细眉毛shader (基于dx11_hair，降低粗糙度)
    2. 创建粗胡子shader (基于dx11_hair，调整颜色和散射)
    3. 创建透明头发shader (基于dx11_hair，增加透明度)
    4. 创建金属质感shader (基于blinn，调整金属度)

    Args:
        base_key: 基础配置键名 (如 "dx11_hair", "blinn")
        overrides: 要覆盖的参数字典
            - "params": shader参数覆盖
            - "texture_map": 纹理映射覆盖
            - "fx_path": shader文件路径覆盖

    Returns:
        应用覆盖后的新配置字典

    Example:
        # 创建细眉毛配置
        fine_eyebrow_config = create_config_with_overrides("dx11_hair", {
            "params": {
                "Roughness": 0.8,      # 增加粗糙度
                "Scatter": 0.3,        # 减少散射
                "Brightness": 0.8      # 降低亮度
            }
        })

        # 创建透明头发配置
        transparent_hair_config = create_config_with_overrides("dx11_hair", {
            "params": {
                "UseHairAlphaTexture": True,
                "transparency": 0.7     # 添加透明度
            },
            "texture_map": {
                "HairAlphaTexture": "_alpha",  # 使用不同的alpha纹理后缀
            }
        })
    """
    base_config = get_shader_config(base_key)
    new_config = base_config.copy()

    # 深拷贝嵌套字典以避免修改原配置
    new_config["params"] = base_config["params"].copy()
    new_config["texture_map"] = base_config["texture_map"].copy()

    # 应用参数覆盖
    if "params" in overrides:
        new_config["params"].update(overrides["params"])

    # 应用纹理映射覆盖
    if "texture_map" in overrides:
        new_config["texture_map"].update(overrides["texture_map"])

    # 应用其他覆盖
    for key, value in overrides.items():
        if key not in ["params", "texture_map"]:
            new_config[key] = value

    return new_config


def get_preset_config(preset_key: str) -> Dict[str, Any]:
    """获取预设的shader配置变体.

    使用PARAMETER_OVERRIDES中定义的预设来创建特殊用途的shader配置。

    Args:
        preset_key: 预设键名 (如 "eyebrow_fine", "beard_coarse")

    Returns:
        预设配置字典

    Raises:
        KeyError: 如果预设键名不存在

    Example:
        # 使用预设创建细眉毛shader
        fine_config = get_preset_config("eyebrow_fine")
        register_shader_config("eyebrow_fine", fine_config)

        # 在资产映射中使用
        const.SUB_ASSET_SHADER_MAP["fine_eyebrow"] = "eyebrow_fine"
    """
    if preset_key not in PARAMETER_OVERRIDES:
        available_presets = list(PARAMETER_OVERRIDES.keys())
        raise KeyError(f"预设 '{preset_key}' 不存在。可用预设: {available_presets}")

    preset = PARAMETER_OVERRIDES[preset_key]
    base_key = preset["base_config"]
    overrides = preset["overrides"]

    return create_config_with_overrides(base_key, overrides)
