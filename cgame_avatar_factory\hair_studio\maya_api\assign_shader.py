# Import built-in modules
import logging
import os
from typing import Dict, <PERSON>, Tu<PERSON>, List

# Import local modules
import cgame_avatar_factory.hair_studio.constants as const
from cgame_avatar_factory.hair_studio.config import get_shader_config

from .utils import get_maya_cmds

# Get Maya commands (real or mock)
cmds = get_maya_cmds(__name__)


class ShaderConfigManager:
    """Manages shader configurations and provides unified interface for shader creation."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def get_shader_config(self, config_key: str) -> Dict[str, Any]:
        """Get shader configuration by key.

        Args:
            config_key: Configuration key

        Returns:
            Shader configuration dictionary
        """
        try:
            return get_shader_config(config_key)
        except KeyError:
            self.logger.warning(f"Unknown shader config: {config_key}, using dx11_hair as default")
            return get_shader_config("dx11_hair")

    def resolve_asset_shader_config(self, asset_type: str, material_name: str = None) -> str:
        """Resolve shader configuration key for given asset type and material.

        Args:
            asset_type: Asset type (hair, eyebrow, beard, scalp)
            material_name: Optional material name for special handling

        Returns:
            Shader configuration key
        """
        # 直接使用constants.py中的配置映射
        shader_mapping = const.SUB_ASSET_SHADER_MAP.get(asset_type, "dx11_hair")

        # Handle dict-based mapping (e.g., eyebrow with _Base special case)
        if isinstance(shader_mapping, dict):
            if material_name and any(key in material_name for key in shader_mapping.keys() if key != "default"):
                # Find matching key
                for key in shader_mapping.keys():
                    if key != "default" and key in material_name:
                        return shader_mapping[key]
            # Use default
            return shader_mapping.get("default", "dx11_hair")

        return shader_mapping


# Global shader config manager instance
shader_config_manager = ShaderConfigManager()


def create_shader_from_config(config_key: str, shader_name: str, texture_path: str = None) -> Tuple[List, str]:
    """Create shader from configuration.

    Args:
        config_key: Shader configuration key
        shader_name: Name for the shader node
        texture_path: Path to texture directory

    Returns:
        Tuple of (created_nodes, shader_node)
    """
    logger = logging.getLogger(__name__)
    config = shader_config_manager.get_shader_config(config_key)

    if config["shader_type"] == "dx11Shader":
        return create_dx11shader_with_tex_params(
            fx_path=config["fx_path"],
            shader_name=shader_name,
            texture_path=texture_path,
            texture_path_dict=config["texture_map"],
            param_dict=config["params"]
        )
    elif config["shader_type"] == "blinn":
        return create_blinn_material_with_texture(
            shader_name=shader_name,
            texture_path=texture_path,
            texture_dict=config["texture_map"]
        )
    else:
        logger.error(f"Unsupported shader type: {config['shader_type']}")
        return [], None


def create_dx11shader_with_tex_params(fx_path, shader_name="myDX11Shader", texture_path=None, texture_path_dict=None, param_dict=None):
    """
    Create a dx11Shader and set shader parameters.

    Args:
        fx_path (str): Path to the .fx file for the dx11Shader.
        shader_name (str, optional): Name of the dx11Shader node. Defaults to "myDX11Shader".
        texture_path (str, optional): Directory containing textures.
        texture_path_dict (dict, optional): Mapping of shader attributes to texture file suffixes.
        param_dict (dict, optional): Dictionary of shader parameter names to values.

    Returns:
        Tuple of (created_file_nodes, shader_node)
    """
    logger = logging.getLogger(__name__)

    # Handle texture path resolution
    if texture_path and os.path.exists(texture_path) and texture_path_dict:
        resolved_textures = find_texture_path(texture_path, texture_path_dict)
    else:
        if texture_path_dict:
            logger.warning(f"No valid texture path provided for {texture_path}, using default parameters only")
            resolved_textures = {key: None for key in texture_path_dict}
        else:
            resolved_textures = {}

    # Merge parameters
    final_params = (param_dict or {}).copy()
    final_params.update(resolved_textures)

    logger.debug(f"Creating dx11Shader with params: {final_params}")

    # Ensure dx11Shader plugin is loaded
    if not cmds.pluginInfo("dx11Shader", query=True, loaded=True):
        try:
            cmds.loadPlugin("dx11Shader")
        except Exception as e:
            logger.error(f"Cannot load dx11Shader plugin: {e}")
            return [], None

    # Create or get dx11Shader node
    if cmds.objExists(shader_name):
        shader = shader_name
    else:
        shader = cmds.shadingNode("dx11Shader", asShader=True, name=shader_name)

    # Set .fx path
    if cmds.attributeQuery("shader", node=shader, exists=True):
        cmds.setAttr(shader + ".shader", fx_path, type="string")
    else:
        logger.error("dx11Shader node has no 'shader' attribute, may not load dx11Shader plugin.")
        return [], None

    # Process parameters
    created_nodes = connect_params_to_shader(shader, final_params)

    return created_nodes, shader


def connect_texture_out_to_shader(file_node, out_attrs, attr_full):
    """Connect texture output to shader attribute.
    Args:
        file_node (str): Name of the file node.
        out_attrs (list): List of output attributes to try.
        attr_full (str): Full name of the shader attribute to connect to.
    """

    for out_attr in out_attrs:
        try:
            cmds.connectAttr(file_node + "." + out_attr, attr_full, force=True)
            break
        except:
            cmds.warning(
                "can not connect texture {0} to attribute: {1}, will try next attribute...".format(out_attr, attr_full),
            )


def connect_params_to_shader(shader_name, param_dict=None):
    """
    Connect parameters to shader.
    Args:
        shader_name (str): Name of the shader node.
        param_dict (dict, optional): Dictionary of shader parameter names to values. Supported value types:
            - str (image file path): Automatically connects a file node as texture.
            - float or int: Sets the attribute directly.
            - tuple or list of length 3: Sets as a color attribute.
            - enum (int or str): Sets enum attribute by index or name.

    Returns:
        list: List of all created file nodes.
    """

    if not param_dict:
        return []

    restore_file_nodes = []

    for attr, value in param_dict.items():
        if not cmds.attributeQuery(attr, node=shader_name, exists=True):
            cmds.warning("dx11Shader node has no attribute: %s" % attr)
            continue

        attr_full = "{0}.{1}".format(shader_name, attr)

        # 1. texture (string and image file)
        if isinstance(value, str) and value.lower().endswith(
            (".png", ".jpg", ".tga", ".tif", ".exr", ".bmp", ".dds"),
        ):
            # NOTE: check file path exists
            if not os.path.exists(value):
                cmds.warning("texture file not exists: %s" % value)
                continue
            # find existing file node
            connections = cmds.listConnections(attr_full, type="file")
            if connections:
                file_node = connections[0]
                cmds.setAttr(file_node + ".fileTextureName", value, type="string")
            else:
                file_node = cmds.shadingNode("file", asTexture=True, isColorManaged=True)
                restore_file_nodes.append(file_node)
                cmds.setAttr(file_node + ".fileTextureName", value, type="string")
                # connect to shader
                if "transparency" in attr:
                    out_attrs = ["outTransparency", "outAlpha"]
                else:
                    out_attrs = ["outColor"]
                connect_texture_out_to_shader(file_node, out_attrs, attr_full)

            continue

        attr_type = cmds.getAttr(attr_full, type=True)
        # 2. color/float3[]/double3/float3[]/color
        if isinstance(value, (tuple, list)) and len(value) == 3 and attr_type in ["float3", "double3", "color"]:
            cmds.setAttr(attr_full, value[0], value[1], value[2], type=attr_type)
            continue

        # 3. float/int
        if isinstance(value, (float, int)) and attr_type in ["float", "double", "long", "short", "byte"]:
            cmds.setAttr(attr_full, value)
            continue

        # 4. enum
        if attr_type == "enum":
            # value can be int or str (enum name)
            if isinstance(value, int):
                cmds.setAttr(attr_full, value)
            elif isinstance(value, str):
                # get all enum names
                enum_names = cmds.attributeQuery(attr, node=shader_name, listEnum=True)[0].split(":")
                if value in enum_names:
                    idx = enum_names.index(value)
                    cmds.setAttr(attr_full, idx)
                else:
                    cmds.warning("enum value %s not in attribute %s options: %s" % (value, attr, enum_names))
            continue

        # 5. other types
        try:
            cmds.setAttr(attr_full, value)
        except Exception as e:
            cmds.warning("can not set attribute: %s, error: %s" % (attr_full, e))

    return restore_file_nodes


def create_blinn_material_with_texture(shader_name, texture_path, texture_dict):
    """
    Create a blinn material and set texture parameters.
    """
    param_dict = find_texture_path(texture_path, texture_dict)
    # create blinn material
    blinn_material = cmds.shadingNode("blinn", asShader=True, name=shader_name)

    # process parameters
    restore_all_nodes = connect_params_to_shader(blinn_material, param_dict)

    return restore_all_nodes, blinn_material


def find_texture_path(root_path, name_dict):
    """
    Recursively search for files under root_path whose filename (without extension) ends with any key in name_dict.
    Return a dict: {key: file_path or None, ...}
    """
    logger = logging.getLogger(__name__)
    result = {key: None for key in name_dict}

    # Handle None or invalid root_path
    if not root_path or not isinstance(root_path, str):
        logger.warning(f"Invalid root_path provided: {root_path}")
        return result

    if not os.path.isdir(root_path):
        logger.warning(f"Root path does not exist or is not a directory: {root_path}")
        return result
    found_keys = set()
    for dirpath, _, filenames in os.walk(root_path):
        for filename in filenames:
            name_no_ext, _ = os.path.splitext(filename)
            for key, value in name_dict.items():
                if value in found_keys:
                    continue
                if name_no_ext.endswith(value):
                    file_path = os.path.join(dirpath, filename)
                    result[key] = file_path
                    found_keys.add(key)
                    logger.debug(f"Found file (no ext match) ending with '{key}': {file_path}")
            if len(found_keys) == len(name_dict):
                break
        if len(found_keys) == len(name_dict):
            break
    return result



def assign_material_to_mesh(mesh_name: str, texture_path: str, shader_name: str = None,
                           asset_type: str = "hair", shader_engine: str = None) -> Tuple[str, List]:
    """Assign material to mesh using modular configuration system.

    Args:
        mesh_name: Name of the mesh to assign material to
        texture_path: Directory containing textures
        shader_name: Optional shader name. Auto-generated if None.
        asset_type: Asset type for shader configuration lookup
        shader_engine: Optional shading group name

    Returns:
        Tuple of (shader_name, created_nodes)
    """
    logger = logging.getLogger(__name__)

    # Check mesh exists first
    if not cmds.objExists(mesh_name):
        logger.error(f"Mesh does not exist: {mesh_name}")
        return None, []

    # Generate shader name if not provided
    if not shader_name:
        shader_name = f"{mesh_name}_shader"

    # 获取asset类型对应的shader映射配置
    shader_mapping = const.SUB_ASSET_SHADER_MAP.get(asset_type, "dx11_hair")

    # Handle complex shader mapping (e.g., eyebrow with special cases)
    if isinstance(shader_mapping, dict):
        return _handle_complex_shader_assignment(
            mesh_name, texture_path, shader_name, shader_mapping, shader_engine
        )

    # Handle simple shader mapping
    config_key = shader_mapping
    created_nodes, shader_node = create_shader_from_config(config_key, shader_name, texture_path)

    if not shader_node:
        logger.error(f"Failed to create shader for {mesh_name}")
        return None, []

    # Create and assign shading group
    _create_and_assign_shading_group(shader_node, mesh_name, shader_engine)

    logger.info(f"Successfully assigned {config_key} shader to {mesh_name}")
    return shader_name, created_nodes


def _handle_complex_shader_assignment(mesh_name: str, texture_path: str, shader_name: str,
                                    shader_mapping: Dict, shader_engine: str = None) -> Tuple[str, List]:
    """Handle complex shader assignment with multiple shader types."""
    # For complex mapping, use replace_shading_node approach
    # Note: shader_engine is not used in replace_shading_node but kept for API consistency
    all_created_nodes = replace_shading_node(mesh_name, shader_mapping, texture_path, shader_name)
    return shader_name, all_created_nodes


def _create_and_assign_shading_group(shader_node: str, mesh_name: str, shader_engine: str = None) -> str:
    """Create shading group and assign to mesh.

    Args:
        shader_node: Name of the shader node
        mesh_name: Name of the mesh to assign to
        shader_engine: Optional shading group name

    Returns:
        Name of the created/used shading group
    """
    logger = logging.getLogger(__name__)

    # Create or get shading group
    sg_name = shader_engine if shader_engine else f"{shader_node}SG"
    if not cmds.objExists(sg_name):
        sg_name = cmds.sets(shader_node, renderable=True, noSurfaceShader=True, empty=True, name=sg_name)
        logger.debug(f"Created shading group: {sg_name}")

    # Connect shader to shading group
    cmds.connectAttr(f"{shader_node}.outColor", f"{sg_name}.surfaceShader", force=True)

    # Assign material to mesh
    cmds.sets(mesh_name, edit=True, forceElement=sg_name)

    return sg_name


def get_all_shadings(obj):
    sg_mat_list = []
    import pymel.core as pm
    sg_list = pm.mel.getConnectedShaders(obj,0)

    for sg in sg_list:
        # mats = (pm.PyNode(sg).listConnections(type="shadingDependNode"))
        surface_mat = cmds.listConnections(sg + '.surfaceShader')
        print(surface_mat)
        
        print(sg)

        sg_mat_list.append((sg, surface_mat))

    return sg_mat_list

def replace_shading_node(obj_name: str, shader_mapping: Dict, texture_path: str, shader_name: str = None) -> List:
    """Replace existing shading nodes with new ones based on configuration.

    Args:
        obj_name: Name of the object to process
        shader_mapping: Dictionary mapping material patterns to shader config keys
        texture_path: Path to texture directory
        shader_name: Base name for new shaders

    Returns:
        List of all created nodes
    """
    logger = logging.getLogger(__name__)
    all_created_nodes = []

    all_shading = get_all_shadings(obj_name)
    if not all_shading:
        logger.warning(f"No shading groups found for object: {obj_name}")
        return all_created_nodes

    default_config_key = shader_mapping.get("default", "dx11_hair")

    for i, (sg, mat_list, _) in enumerate(all_shading):
        if not mat_list:
            logger.warning(f"No materials found for shading group: {sg}")
            continue

        mat = mat_list[0]  # Use first material
        mat_name = mat if isinstance(mat, str) else mat.name()

        # Determine shader config based on material name
        config_key = default_config_key
        for pattern, pattern_config_key in shader_mapping.items():
            if pattern != "default" and pattern in mat_name:
                config_key = pattern_config_key
                break

        # Generate unique shader name
        current_shader_name = f"{shader_name}_{i}" if shader_name else f"{obj_name}_shader_{i}"

        # Create new shader
        created_nodes, new_shader = create_shader_from_config(config_key, current_shader_name, texture_path)

        if new_shader:
            # Connect new shader to existing shading group
            try:
                cmds.connectAttr(f"{new_shader}.outColor", f"{sg}.surfaceShader", force=True)
                all_created_nodes.extend(created_nodes)
                logger.info(f"Replaced shader in {sg} with {config_key} shader: {new_shader}")
            except Exception as e:
                logger.error(f"Failed to connect shader {new_shader} to {sg}: {e}")
        else:
            logger.error(f"Failed to create shader for {sg}")

    return all_created_nodes
    